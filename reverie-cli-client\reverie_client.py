#!/usr/bin/env python3
"""
Reverie CLI Client - A command-line client for Reverie CLI Server Mode

This client provides a command-line interface to interact with Reverie CLI
server, supporting file operations, web search, AI assistance, and more.
"""

import os
import sys
import json
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

# Import aiohttp with error handling
try:
    import aiohttp
except ImportError:
    print("❌ aiohttp not installed. Please install with: pip install aiohttp")
    sys.exit(1)

# Import readline for command completion (with fallback)
try:
    import readline
    import rlcompleter
    READLINE_AVAILABLE = True
except ImportError:
    READLINE_AVAILABLE = False
    print("⚠️  readline not available - command completion disabled")


class CommandCompleter:
    """Command completion handler for readline."""

    def __init__(self):
        """Initialize the completer with available commands."""
        self.commands = [
            'help', 'ls', 'cat', 'search', 'models', 'status',
            'chat', 'ask', 'agent', 'exit', 'quit'
        ]

        # Command options
        self.command_options = {
            'chat': ['--model', '--stream', '--enhanced'],
            'ask': ['--model', '--stream', '--enhanced'],
            'agent': ['--type', '--project'],
            'models': ['load', 'unload', 'current'],
            'ls': [],
            'cat': [],
            'search': [],
            'status': [],
            'help': [],
            'exit': [],
            'quit': []
        }

        # Task types for agent command
        self.agent_task_types = [
            'code', 'analyze', 'search', 'debug', 'review',
            'optimize', 'security', 'general'
        ]

    def complete(self, text, state):
        """Complete command based on current input."""
        try:
            # Get the current line
            line = readline.get_line_buffer()
            words = line.split()

            # Store matches for this completion session
            if not hasattr(self, '_current_matches') or getattr(self, '_last_text', '') != text:
                self._last_text = text

                if not words or (len(words) == 1 and not line.endswith(' ')):
                    # Complete main commands
                    self._current_matches = [cmd for cmd in self.commands if cmd.startswith(text)]
                else:
                    # Complete subcommands or options
                    command = words[0].lower()
                    self._current_matches = self._complete_subcommand(command, words, text, line)

            if state < len(self._current_matches):
                return self._current_matches[state]
            else:
                return None

        except Exception:
            return None

    def _complete_subcommand(self, command: str, words: List[str], text: str, line: str) -> List[str]:
        """Complete subcommands and options for a specific command."""
        matches = []

        if command == 'models':
            # Complete models subcommands
            if len(words) == 2 and not line.endswith(' '):
                # Completing the subcommand
                subcommands = ['load', 'unload', 'current']
                matches = [sub for sub in subcommands if sub.startswith(text)]

        elif command in ['chat', 'ask']:
            # Complete chat options
            if text.startswith('--'):
                options = ['--model', '--stream', '--enhanced']
                matches = [opt for opt in options if opt.startswith(text)]

        elif command == 'agent':
            # Complete agent options and task types
            if text.startswith('--'):
                options = ['--type', '--project']
                matches = [opt for opt in options if opt.startswith(text)]
            elif '--type' in words:
                # Complete task types after --type
                try:
                    type_index = words.index('--type')
                    if len(words) > type_index + 1:
                        # We're completing the task type
                        matches = [t for t in self.agent_task_types if t.startswith(text)]
                except ValueError:
                    pass

        return matches


class ReverieClient:
    """Reverie CLI Client for interacting with server mode."""

    def __init__(self, server_host: str = "localhost", server_port: int = 8000):
        """Initialize the client.

        Args:
            server_host: Server hostname or IP
            server_port: Server port number
        """
        self.server_host = server_host
        self.server_port = server_port
        self.base_url = f"http://{server_host}:{server_port}"
        self.session: Optional[aiohttp.ClientSession] = None
        self.running = True
        self.completer = CommandCompleter() if READLINE_AVAILABLE else None
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def print_banner(self):
        """Print the Reverie CLI banner."""
        banner = """
██████╗ ███████╗██╗   ██╗███████╗██████╗ ██╗███████╗     ██████╗██╗     ██╗
██╔══██╗██╔════╝██║   ██║██╔════╝██╔══██╗██║██╔════╝    ██╔════╝██║     ██║
██████╔╝█████╗  ██║   ██║█████╗  ██████╔╝██║█████╗      ██║     ██║     ██║
██╔══██╗██╔══╝  ╚██╗ ██╔╝██╔══╝  ██╔══██╗██║██╔══╝      ██║     ██║     ██║
██║  ██║███████╗ ╚████╔╝ ███████╗██║  ██║██║███████╗    ╚██████╗███████╗██║
╚═╝  ╚═╝╚══════╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝╚══════╝     ╚═════╝╚══════╝╚═╝
                                                                              
        AI-Native Development Assistant - Client Mode
        Connected to: {self.base_url}
        """
        print(banner)
    
    async def check_server_connection(self) -> bool:
        """Check if server is accessible."""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Connected to Reverie CLI Server")
                    print(f"   Server: {data.get('service', 'Unknown')}")
                    print(f"   Version: {data.get('version', 'Unknown')}")
                    print(f"   Status: {data.get('status', 'Unknown')}")
                    return True
                else:
                    print(f"❌ Server responded with status {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Failed to connect to server: {e}")
            print(f"   Make sure the server is running at {self.base_url}")
            return False

    def prompt_custom_server(self) -> bool:
        """Prompt user for custom server URL."""
        print("\n" + "=" * 70)
        print("🔧 Server Connection Configuration")
        print("=" * 70)
        print("Default server connection failed. You can:")
        print("1. Enter a custom server URL")
        print("2. Exit the application")
        print()

        while True:
            choice = input("Enter your choice (1/2): ").strip()
            if choice == "1":
                while True:
                    custom_url = input("Enter custom server URL (e.g., http://*************:8000): ").strip()
                    if custom_url:
                        # Parse the URL to extract host and port
                        try:
                            if not custom_url.startswith(('http://', 'https://')):
                                custom_url = 'http://' + custom_url

                            from urllib.parse import urlparse
                            parsed = urlparse(custom_url)

                            if parsed.hostname:
                                self.server_host = parsed.hostname
                                self.server_port = parsed.port or 8000
                                self.base_url = f"http://{self.server_host}:{self.server_port}"
                                print(f"✅ Server URL updated to: {self.base_url}")
                                return True
                            else:
                                print("❌ Invalid URL format. Please try again.")
                        except Exception as e:
                            print(f"❌ Error parsing URL: {e}")
                    else:
                        print("❌ Please enter a valid URL.")
            elif choice == "2":
                print("👋 Goodbye!")
                return False
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")
    
    async def api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make an API request to the server.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without /api/v1 prefix)
            data: Request data for POST requests
            
        Returns:
            Response data as dictionary
        """
        url = urljoin(f"{self.base_url}/api/v1/", endpoint.lstrip('/'))
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await self._handle_response(response)
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await self._handle_response(response)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error: {e}")
    
    async def _handle_response(self, response: aiohttp.ClientResponse) -> Dict[str, Any]:
        """Handle API response."""
        if response.status == 200:
            return await response.json()
        else:
            error_text = await response.text()
            raise Exception(f"API error {response.status}: {error_text}")
    
    async def cmd_help(self, args: List[str]):
        """Show help information."""
        help_text = """
🎯 Reverie CLI Client Commands

📁 File Operations:
  ls [path]              - List directory contents
  cat <file>             - Display file contents
  mkdir <dir>            - Create directory
  rm <file>              - Delete file
  touch <file>           - Create empty file
  pwd                    - Show current directory

🔍 Web & Search:
  search <query>         - Web search
  fetch <url>            - Fetch web page content

🤖 AI & Chat:
  chat <message>         - Chat with AI
    --model <name>       - Use specific model
    --stream             - Enable streaming response
    --enhanced           - Use enhanced chat with web search
  ask <question>         - Alias for chat command

🚀 AI Agent:
  agent <task>           - Execute AI agent tasks (auto-selects best approach)
    --type <type>        - Task type (optional: code, analyze, search, debug, review, optimize, security)
    --project <path>     - Project path for context (optional)

🤖 Model Management:
  models                 - List all available models
  models load <name>     - Load a specific model
  models unload          - Unload current model
  models current         - Show current model info

⚙️ System:
  status                 - Show server status
  tools                  - List available tools
  help                   - Show this help
  exit                   - Exit client

💡 Examples:
  chat "Explain async programming in Python"
  chat "Write a function" --enhanced --model gpt-4
  agent "Analyze this codebase" --project ./src
  agent "Debug memory leak in my application"
  models load Lucy-128k
  search "Python best practices"
        """
        print(help_text)
    
    async def cmd_ls(self, args: List[str]):
        """List directory contents."""
        path = args[0] if args else "."
        try:
            result = await self.api_request("POST", "/files/list", {"path": path})
            
            print(f"\n📁 Directory: {result.get('path', path)}")
            print("=" * 50)
            
            items = result.get('items', [])
            if not items:
                print("(empty directory)")
                return
            
            for item in items:
                icon = "📁" if item.get('type') == 'directory' else "📄"
                size = f"({item.get('size', 0)} bytes)" if item.get('type') == 'file' else ""
                print(f"{icon} {item.get('name', '')} {size}")
                
        except Exception as e:
            print(f"❌ Failed to list directory: {e}")
    
    async def cmd_cat(self, args: List[str]):
        """Display file contents."""
        if not args:
            print("❌ Please specify a file to read")
            return
            
        file_path = args[0]
        try:
            result = await self.api_request("POST", "/files/read", {"path": file_path})
            
            print(f"\n📄 File: {file_path}")
            print("=" * 50)
            print(result.get('content', ''))
            
        except Exception as e:
            print(f"❌ Failed to read file: {e}")
    
    async def cmd_search(self, args: List[str]):
        """Perform web search."""
        if not args:
            print("❌ Please specify a search query")
            return
            
        query = " ".join(args)
        try:
            result = await self.api_request("POST", "/web/search", {
                "query": query,
                "num_results": 5
            })
            
            print(f"\n🔍 Search Results for: {query}")
            print("=" * 50)
            
            for i, item in enumerate(result.get('results', []), 1):
                print(f"{i}. {item.get('title', 'No title')}")
                print(f"   URL: {item.get('url', '')}")
                print(f"   {item.get('snippet', 'No description')}")
                print()
                
        except Exception as e:
            print(f"❌ Web search failed: {e}")
    
    async def cmd_models(self, args: List[str]):
        """Enhanced model management - list, load, unload models."""
        if not args:
            # List all models
            try:
                result = await self.api_request("GET", "/models")

                print("\n🤖 Available Models")
                print("=" * 60)

                models = result.get('models', [])
                if not models:
                    print("No models found")
                    return

                current_model = result.get('current_model')

                for model in models:
                    status_icon = {
                        "loaded": "🟢",
                        "available": "⚪",
                        "loading": "🟡",
                        "error": "🔴"
                    }.get(model.get('status'), "❓")

                    name = model.get('name', 'Unknown')
                    backend = model.get('backend', 'Unknown')
                    current_marker = " ← CURRENT" if name == current_model else ""

                    print(f"{status_icon} {name} ({backend}){current_marker}")

                    # Show additional info if available
                    if model.get('size'):
                        print(f"    📏 Size: {model['size']}")
                    if model.get('description'):
                        print(f"    📝 {model['description']}")

                print(f"\n💡 Usage: models load <name> | models unload | models current")

            except Exception as e:
                print(f"❌ Failed to list models: {e}")

        elif args[0] == "load":
            # Load a specific model
            if len(args) < 2:
                print("❌ Please specify a model name")
                print("💡 Usage: models load <model_name>")
                return

            model_name = args[1]
            try:
                print(f"⏳ Loading model: {model_name}")
                result = await self.api_request("POST", "/models/load", {
                    "model_name": model_name
                })

                if result.get('success'):
                    print(f"✅ Model '{model_name}' loaded successfully")
                    if result.get('info'):
                        print(f"📋 Info: {result['info']}")
                else:
                    print(f"❌ Failed to load model: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ Failed to load model: {e}")

        elif args[0] == "unload":
            # Unload current model
            try:
                print("⏳ Unloading current model...")
                result = await self.api_request("POST", "/models/unload")

                if result.get('success'):
                    print("✅ Model unloaded successfully")
                else:
                    print(f"❌ Failed to unload model: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ Failed to unload model: {e}")

        elif args[0] == "current":
            # Show current model info
            try:
                result = await self.api_request("GET", "/models/current")

                if result.get('model'):
                    model = result['model']
                    print(f"\n🤖 Current Model")
                    print("=" * 40)
                    print(f"📛 Name: {model.get('name', 'Unknown')}")
                    print(f"🔧 Backend: {model.get('backend', 'Unknown')}")
                    print(f"📊 Status: {model.get('status', 'Unknown')}")
                    if model.get('size'):
                        print(f"📏 Size: {model['size']}")
                    if model.get('loaded_at'):
                        print(f"⏰ Loaded: {model['loaded_at']}")
                else:
                    print("No model currently loaded")

            except Exception as e:
                print(f"❌ Failed to get current model: {e}")

        else:
            print(f"❌ Unknown models command: {args[0]}")
            print("💡 Available commands: load, unload, current")
    
    async def cmd_status(self, args: List[str]):
        """Show server status."""
        try:
            result = await self.api_request("GET", "/health")
            
            print("\n⚙️ Server Status")
            print("=" * 50)
            print(f"Service: {result.get('service', 'Unknown')}")
            print(f"Version: {result.get('version', 'Unknown')}")
            print(f"Status: {result.get('status', 'Unknown')}")
            print(f"Uptime: {result.get('uptime', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Failed to get server status: {e}")
    
    async def cmd_chat(self, args: List[str]):
        """Enhanced chat with AI - supports various options."""
        if not args:
            print("❌ Please provide a message")
            print("💡 Usage: chat <message> [--model <model>] [--stream] [--enhanced]")
            return

        # Parse arguments
        message_parts = []
        model = None
        stream = False
        enhanced = False

        i = 0
        while i < len(args):
            if args[i] == "--model" and i + 1 < len(args):
                model = args[i + 1]
                i += 2
            elif args[i] == "--stream":
                stream = True
                i += 1
            elif args[i] == "--enhanced":
                enhanced = True
                i += 1
            else:
                message_parts.append(args[i])
                i += 1

        if not message_parts:
            print("❌ Please provide a message")
            return

        message = " ".join(message_parts)

        try:
            # Choose endpoint based on enhanced flag
            endpoint = "/chat/enhanced" if enhanced else "/chat/completions"

            # Prepare request data
            request_data = {
                "messages": [{"role": "user", "content": message}],
                "stream": stream
            }

            if model:
                request_data["model"] = model

            if enhanced:
                request_data.update({
                    "context_type": "general",
                    "use_web_search": True,
                    "remember_conversation": True
                })

            print(f"\n🤖 AI Response ({endpoint.split('/')[-1]}):")
            print("=" * 60)

            if stream:
                # Handle streaming response (simplified for now)
                result = await self.api_request("POST", endpoint, request_data)
                choices = result.get('choices', [])
                if choices:
                    content = choices[0].get('message', {}).get('content', 'No response')
                    print(content)
                else:
                    print("No response received")
            else:
                result = await self.api_request("POST", endpoint, request_data)
                choices = result.get('choices', [])
                if choices:
                    content = choices[0].get('message', {}).get('content', 'No response')
                    print(content)

                    # Show additional info for enhanced chat
                    if enhanced and 'usage' in result:
                        usage = result['usage']
                        print(f"\n📊 Usage: {usage.get('total_tokens', 0)} tokens")
                else:
                    print("No response received")

        except Exception as e:
            print(f"❌ Chat failed: {e}")

    async def cmd_agent(self, args: List[str]):
        """Execute AI agent tasks with intelligent auto-selection."""
        if not args:
            print("❌ Please provide a task description")
            print("💡 Usage: agent <task_description> [--type <task_type>] [--project <path>]")
            print("🤖 The AI will automatically select the best approach and tools!")
            print("📋 Optional task types: code, analyze, search, debug, review, optimize, security")
            return

        # Parse arguments (simplified)
        task_parts = []
        task_type = "auto"  # Let AI decide
        project_path = None

        i = 0
        while i < len(args):
            if args[i] == "--type" and i + 1 < len(args):
                task_type = args[i + 1]
                i += 2
            elif args[i] == "--project" and i + 1 < len(args):
                project_path = args[i + 1]
                i += 2
            else:
                task_parts.append(args[i])
                i += 1

        if not task_parts:
            print("❌ Please provide a task description")
            return

        description = " ".join(task_parts)

        try:
            print(f"\n🤖 Executing Intelligent Agent Task")
            print("=" * 60)
            print(f"📝 Task: {description}")
            if task_type != "auto":
                print(f"🎯 Type: {task_type}")
            if project_path:
                print(f"📁 Project: {project_path}")
            print("🧠 AI is analyzing and selecting optimal approach...")
            print("⏳ Processing...")

            # Prepare simplified agent task request
            request_data = {
                "task_type": task_type,
                "description": description,
                "auto_select_engines": True,
                "priority": "normal",
                "remember_result": True
            }

            if project_path:
                request_data["project_path"] = project_path

            result = await self.api_request("POST", "/agent/execute", request_data)

            print(f"\n✅ Task Completed!")
            print(f"🆔 Task ID: {result.get('task_id', 'Unknown')}")
            print(f"⏱️  Execution Time: {result.get('execution_time', 0):.2f}s")
            print(f"🔧 Engines Used: {', '.join(result.get('engines_used', []))}")

            # Display result
            task_result = result.get('result', {})
            if 'error' in task_result:
                print(f"\n❌ Error: {task_result['error']}")
            else:
                print(f"\n📋 Result:")
                print("-" * 40)

                # Format result based on task type
                if task_type == "code":
                    if 'code' in task_result:
                        print(f"💻 Generated Code:\n{task_result['code']}")
                    if 'explanation' in task_result:
                        print(f"\n📖 Explanation:\n{task_result['explanation']}")
                elif task_type == "analyze":
                    if 'analysis' in task_result:
                        print(f"🔍 Analysis:\n{task_result['analysis']}")
                    if 'recommendations' in task_result:
                        print(f"\n💡 Recommendations:\n{task_result['recommendations']}")
                else:
                    # General result display
                    for key, value in task_result.items():
                        if key != 'error':
                            print(f"{key.title()}: {value}")

            # Show suggestions
            suggestions = result.get('suggestions', [])
            if suggestions:
                print(f"\n💡 Suggestions:")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")

        except Exception as e:
            print(f"❌ Agent task failed: {e}")
    
    async def process_command(self, command_line: str):
        """Process a command line input."""
        if not command_line.strip():
            return
            
        parts = command_line.strip().split()
        command = parts[0].lower()
        args = parts[1:]
        
        # Command mapping
        commands = {
            "help": self.cmd_help,
            "ls": self.cmd_ls,
            "cat": self.cmd_cat,
            "search": self.cmd_search,
            "models": self.cmd_models,
            "status": self.cmd_status,
            "chat": self.cmd_chat,
            "ask": self.cmd_chat,  # Alias for chat
            "agent": self.cmd_agent,
            "exit": lambda args: setattr(self, 'running', False),
            "quit": lambda args: setattr(self, 'running', False),
        }
        
        if command in commands:
            await commands[command](args)
        else:
            print(f"❌ Unknown command: {command}")
            print("Type 'help' for available commands")

    def setup_readline(self):
        """Setup readline for command completion."""
        if not READLINE_AVAILABLE or not self.completer:
            return

        try:
            # Set up completion
            readline.set_completer(self.completer.complete)
            readline.parse_and_bind('tab: complete')

            # Enable history
            readline.parse_and_bind('set editing-mode emacs')
            readline.parse_and_bind('set completion-ignore-case on')
            readline.parse_and_bind('set show-all-if-ambiguous on')

            # Set up history file
            history_file = os.path.expanduser('~/.reverie_cli_history')
            try:
                readline.read_history_file(history_file)
                # Limit history size
                readline.set_history_length(1000)
            except FileNotFoundError:
                pass  # History file doesn't exist yet

            # Save history on exit
            import atexit
            atexit.register(lambda: self._save_history(history_file))

        except Exception as e:
            print(f"⚠️  Failed to setup command completion: {e}")

    def _save_history(self, history_file: str):
        """Save command history to file."""
        try:
            if READLINE_AVAILABLE:
                readline.write_history_file(history_file)
        except Exception:
            pass  # Ignore errors when saving history

    async def run_interactive(self):
        """Run the interactive client."""
        self.print_banner()

        # Setup command completion
        self.setup_readline()

        # Check server connection
        if not await self.check_server_connection():
            # If default connection fails, prompt for custom server
            if not self.prompt_custom_server():
                return

            # Try connecting to custom server
            if not await self.check_server_connection():
                print("❌ Failed to connect to custom server. Exiting.")
                return

        print("\n" + "=" * 70)
        if READLINE_AVAILABLE:
            print("Type 'help' for available commands, 'exit' to quit")
            print("💡 Use Tab for command completion, ↑↓ for history")
        else:
            print("Type 'help' for available commands, 'exit' to quit")
        print("=" * 70)

        while self.running:
            try:
                if READLINE_AVAILABLE:
                    command_line = input("\nReverie CLI> ").strip()
                else:
                    command_line = input("\nReverie CLI> ").strip()

                if command_line:
                    await self.process_command(command_line)
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except EOFError:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Reverie CLI Client")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--command", help="Single command to execute")
    
    args = parser.parse_args()
    
    async with ReverieClient(args.host, args.port) as client:
        if args.command:
            # Execute single command
            await client.process_command(args.command)
        else:
            # Interactive mode
            await client.run_interactive()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
