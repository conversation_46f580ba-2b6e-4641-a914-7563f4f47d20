#!/usr/bin/env python3
"""
Reverie CLI Client - A command-line client for Reverie CLI Server Mode

This client provides a command-line interface to interact with Reverie CLI
server, supporting file operations, web search, AI assistance, and more.
"""

import os
import sys
import json
import asyncio
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, List
from urllib.parse import urljoin

# Import aiohttp with error handling
try:
    import aiohttp
except ImportError:
    print("❌ aiohttp not installed. Please install with: pip install aiohttp")
    sys.exit(1)


class ReverieClient:
    """Reverie CLI Client for interacting with server mode."""
    
    def __init__(self, server_host: str = "localhost", server_port: int = 8000):
        """Initialize the client.
        
        Args:
            server_host: Server hostname or IP
            server_port: Server port number
        """
        self.server_host = server_host
        self.server_port = server_port
        self.base_url = f"http://{server_host}:{server_port}"
        self.session: Optional[aiohttp.ClientSession] = None
        self.running = True
        
    async def __aenter__(self):
        """Async context manager entry."""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    def print_banner(self):
        """Print the Reverie CLI banner."""
        banner = """
██████╗ ███████╗██╗   ██╗███████╗██████╗ ██╗███████╗     ██████╗██╗     ██╗
██╔══██╗██╔════╝██║   ██║██╔════╝██╔══██╗██║██╔════╝    ██╔════╝██║     ██║
██████╔╝█████╗  ██║   ██║█████╗  ██████╔╝██║█████╗      ██║     ██║     ██║
██╔══██╗██╔══╝  ╚██╗ ██╔╝██╔══╝  ██╔══██╗██║██╔══╝      ██║     ██║     ██║
██║  ██║███████╗ ╚████╔╝ ███████╗██║  ██║██║███████╗    ╚██████╗███████╗██║
╚═╝  ╚═╝╚══════╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝╚══════╝     ╚═════╝╚══════╝╚═╝
                                                                              
        AI-Native Development Assistant - Client Mode
        Connected to: {self.base_url}
        """
        print(banner)
    
    async def check_server_connection(self) -> bool:
        """Check if server is accessible."""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Connected to Reverie CLI Server")
                    print(f"   Server: {data.get('service', 'Unknown')}")
                    print(f"   Version: {data.get('version', 'Unknown')}")
                    print(f"   Status: {data.get('status', 'Unknown')}")
                    return True
                else:
                    print(f"❌ Server responded with status {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Failed to connect to server: {e}")
            print(f"   Make sure the server is running at {self.base_url}")
            return False
    
    async def api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make an API request to the server.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without /api/v1 prefix)
            data: Request data for POST requests
            
        Returns:
            Response data as dictionary
        """
        url = urljoin(f"{self.base_url}/api/v1/", endpoint.lstrip('/'))
        
        try:
            if method.upper() == "GET":
                async with self.session.get(url) as response:
                    return await self._handle_response(response)
            elif method.upper() == "POST":
                async with self.session.post(url, json=data) as response:
                    return await self._handle_response(response)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            raise Exception(f"Network error: {e}")
    
    async def _handle_response(self, response: aiohttp.ClientResponse) -> Dict[str, Any]:
        """Handle API response."""
        if response.status == 200:
            return await response.json()
        else:
            error_text = await response.text()
            raise Exception(f"API error {response.status}: {error_text}")
    
    async def cmd_help(self, args: List[str]):
        """Show help information."""
        help_text = """
🎯 Reverie CLI Client Commands

📁 File Operations:
  ls [path]              - List directory contents
  cat <file>             - Display file contents  
  mkdir <dir>            - Create directory
  rm <file>              - Delete file
  touch <file>           - Create empty file
  pwd                    - Show current directory

🔍 Web & Search:
  search <query>         - Web search
  fetch <url>            - Fetch web page content
  
🤖 AI & Models:
  models                 - List available models
  load <model>           - Load a model
  chat <message>         - Chat with AI
  ask <question>         - Ask AI a question
  
⚙️ System:
  status                 - Show server status
  tools                  - List available tools
  help                   - Show this help
  exit                   - Exit client
  
💡 Examples:
  search "Python async programming"
  cat main.py
  ask "How to optimize this code?"
  models
  load my-model
        """
        print(help_text)
    
    async def cmd_ls(self, args: List[str]):
        """List directory contents."""
        path = args[0] if args else "."
        try:
            result = await self.api_request("POST", "/files/list", {"path": path})
            
            print(f"\n📁 Directory: {result.get('path', path)}")
            print("=" * 50)
            
            items = result.get('items', [])
            if not items:
                print("(empty directory)")
                return
            
            for item in items:
                icon = "📁" if item.get('type') == 'directory' else "📄"
                size = f"({item.get('size', 0)} bytes)" if item.get('type') == 'file' else ""
                print(f"{icon} {item.get('name', '')} {size}")
                
        except Exception as e:
            print(f"❌ Failed to list directory: {e}")
    
    async def cmd_cat(self, args: List[str]):
        """Display file contents."""
        if not args:
            print("❌ Please specify a file to read")
            return
            
        file_path = args[0]
        try:
            result = await self.api_request("POST", "/files/read", {"path": file_path})
            
            print(f"\n📄 File: {file_path}")
            print("=" * 50)
            print(result.get('content', ''))
            
        except Exception as e:
            print(f"❌ Failed to read file: {e}")
    
    async def cmd_search(self, args: List[str]):
        """Perform web search."""
        if not args:
            print("❌ Please specify a search query")
            return
            
        query = " ".join(args)
        try:
            result = await self.api_request("POST", "/web/search", {
                "query": query,
                "num_results": 5
            })
            
            print(f"\n🔍 Search Results for: {query}")
            print("=" * 50)
            
            for i, item in enumerate(result.get('results', []), 1):
                print(f"{i}. {item.get('title', 'No title')}")
                print(f"   URL: {item.get('url', '')}")
                print(f"   {item.get('snippet', 'No description')}")
                print()
                
        except Exception as e:
            print(f"❌ Web search failed: {e}")
    
    async def cmd_models(self, args: List[str]):
        """List available models."""
        try:
            result = await self.api_request("GET", "/models")
            
            print("\n🤖 Available Models")
            print("=" * 50)
            
            for model in result.get('models', []):
                status_icon = {"loaded": "🟢", "available": "⚪", "error": "🔴"}.get(
                    model.get('status'), "❓"
                )
                print(f"{status_icon} {model.get('name', '')} ({model.get('backend', '')})")
                
        except Exception as e:
            print(f"❌ Failed to list models: {e}")
    
    async def cmd_status(self, args: List[str]):
        """Show server status."""
        try:
            result = await self.api_request("GET", "/health")
            
            print("\n⚙️ Server Status")
            print("=" * 50)
            print(f"Service: {result.get('service', 'Unknown')}")
            print(f"Version: {result.get('version', 'Unknown')}")
            print(f"Status: {result.get('status', 'Unknown')}")
            print(f"Uptime: {result.get('uptime', 'Unknown')}")
            
        except Exception as e:
            print(f"❌ Failed to get server status: {e}")
    
    async def cmd_chat(self, args: List[str]):
        """Chat with AI."""
        if not args:
            print("❌ Please provide a message")
            return
            
        message = " ".join(args)
        try:
            result = await self.api_request("POST", "/chat/completions", {
                "messages": [{"role": "user", "content": message}],
                "stream": False
            })
            
            print(f"\n🤖 AI Response:")
            print("=" * 50)
            
            choices = result.get('choices', [])
            if choices:
                print(choices[0].get('message', {}).get('content', 'No response'))
            else:
                print("No response received")
                
        except Exception as e:
            print(f"❌ Chat failed: {e}")
    
    async def process_command(self, command_line: str):
        """Process a command line input."""
        if not command_line.strip():
            return
            
        parts = command_line.strip().split()
        command = parts[0].lower()
        args = parts[1:]
        
        # Command mapping
        commands = {
            "help": self.cmd_help,
            "ls": self.cmd_ls,
            "cat": self.cmd_cat,
            "search": self.cmd_search,
            "models": self.cmd_models,
            "status": self.cmd_status,
            "chat": self.cmd_chat,
            "ask": self.cmd_chat,  # Alias for chat
            "exit": lambda args: setattr(self, 'running', False),
            "quit": lambda args: setattr(self, 'running', False),
        }
        
        if command in commands:
            await commands[command](args)
        else:
            print(f"❌ Unknown command: {command}")
            print("Type 'help' for available commands")
    
    async def run_interactive(self):
        """Run the interactive client."""
        self.print_banner()
        
        # Check server connection
        if not await self.check_server_connection():
            return
        
        print("\n" + "=" * 70)
        print("Type 'help' for available commands, 'exit' to quit")
        print("=" * 70)
        
        while self.running:
            try:
                command_line = input("\nReverie CLI> ").strip()
                if command_line:
                    await self.process_command(command_line)
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye!")
                break
            except EOFError:
                print("\n\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Reverie CLI Client")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--command", help="Single command to execute")
    
    args = parser.parse_args()
    
    async with ReverieClient(args.host, args.port) as client:
        if args.command:
            # Execute single command
            await client.process_command(args.command)
        else:
            # Interactive mode
            await client.run_interactive()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
