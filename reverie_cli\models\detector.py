"""
Model type detection and automatic backend selection.
"""

import json
from pathlib import Path
from typing import Optional, List, Dict, Tuple
from dataclasses import dataclass

from reverie_cli.core.logging import get_logger
from reverie_cli.models.info import ModelInfo, ModelStatus, ModelBackend


logger = get_logger("model_detector")


@dataclass
class DetectedModel:
    """Information about a detected model."""
    name: str
    path: Path
    backend: ModelBackend
    model_type: str
    size_gb: Optional[float] = None
    context_length: Optional[int] = None
    architecture: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[Dict] = None


class ModelDetector:
    """Automatic model type detection and backend selection."""
    
    def __init__(self, models_dir: Optional[Path] = None):
        """Initialize the model detector.
        
        Args:
            models_dir: Path to models directory. Defaults to ./models/llm
        """
        if models_dir is None:
            models_dir = Path.cwd() / "models" / "llm"
        
        self.models_dir = Path(models_dir)
        self.logger = get_logger("model_detector")
        
        # Ensure directories exist
        self.models_dir.mkdir(parents=True, exist_ok=True)
        (self.models_dir / "transformers").mkdir(exist_ok=True)
        (self.models_dir / "gguf").mkdir(exist_ok=True)
    
    def scan_for_models(self) -> List[DetectedModel]:
        """Scan the models directory for available models.

        Returns:
            List of detected models
        """
        detected_models = []

        # Scan for GGUF single file models
        detected_models.extend(self._scan_gguf_models())

        # Scan for Transformers directory models
        detected_models.extend(self._scan_transformers_models())

        # Scan for any other model files in the root models directory
        detected_models.extend(self._scan_root_models())

        self.logger.info(f"Detected {len(detected_models)} models")
        return detected_models
    
    def _scan_gguf_models(self) -> List[DetectedModel]:
        """Scan for GGUF models."""
        gguf_dir = self.models_dir / "gguf"
        models = []
        
        if not gguf_dir.exists():
            return models
        
        for gguf_file in gguf_dir.glob("*.gguf"):
            try:
                model = self._detect_gguf_model(gguf_file)
                if model:
                    models.append(model)
            except Exception as e:
                self.logger.warning(f"Failed to detect GGUF model {gguf_file}: {e}")
        
        return models
    
    def _scan_transformers_models(self) -> List[DetectedModel]:
        """Scan for Transformers models."""
        transformers_dir = self.models_dir / "transformers"
        models = []
        
        if not transformers_dir.exists():
            return models
        
        for model_dir in transformers_dir.iterdir():
            if model_dir.is_dir():
                try:
                    model = self._detect_transformers_model(model_dir)
                    if model:
                        models.append(model)
                except Exception as e:
                    self.logger.warning(f"Failed to detect Transformers model {model_dir}: {e}")
        
        return models

    def _scan_root_models(self) -> List[DetectedModel]:
        """Scan for models in the root models directory."""
        models = []

        if not self.models_dir.exists():
            return models

        # Look for GGUF files in the root directory
        for gguf_file in self.models_dir.glob("*.gguf"):
            try:
                model = self._detect_gguf_model(gguf_file)
                if model:
                    models.append(model)
            except Exception as e:
                self.logger.warning(f"Failed to detect GGUF model {gguf_file}: {e}")

        # Look for model directories in the root directory
        for model_dir in self.models_dir.iterdir():
            if model_dir.is_dir() and model_dir.name not in ["transformers", "gguf"]:
                try:
                    model = self._detect_transformers_model(model_dir)
                    if model:
                        models.append(model)
                except Exception as e:
                    self.logger.warning(f"Failed to detect Transformers model {model_dir}: {e}")

        return models

    def _detect_gguf_model(self, gguf_file: Path) -> Optional[DetectedModel]:
        """Detect a GGUF model file.
        
        Args:
            gguf_file: Path to GGUF file
            
        Returns:
            DetectedModel or None if detection fails
        """
        try:
            # Get file size
            size_bytes = gguf_file.stat().st_size
            size_gb = size_bytes / (1024 ** 3)
            
            # Extract model name from filename
            model_name = gguf_file.stem
            
            # Try to extract metadata from filename patterns
            metadata = self._extract_gguf_metadata(model_name)
            
            return DetectedModel(
                name=model_name,
                path=gguf_file,
                backend=ModelBackend.GGUF,
                model_type="gguf",
                size_gb=round(size_gb, 1),
                context_length=metadata.get("context_length"),
                architecture=metadata.get("architecture"),
                description=f"GGUF model ({size_gb:.1f}GB)",
                metadata=metadata
            )
            
        except Exception as e:
            self.logger.error(f"Failed to detect GGUF model {gguf_file}: {e}")
            return None
    
    def _detect_transformers_model(self, model_dir: Path) -> Optional[DetectedModel]:
        """Detect a Transformers model directory.
        
        Args:
            model_dir: Path to model directory
            
        Returns:
            DetectedModel or None if detection fails
        """
        try:
            # Check for required files
            config_file = model_dir / "config.json"
            if not config_file.exists():
                return None
            
            # Load configuration
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Calculate directory size
            size_bytes = sum(f.stat().st_size for f in model_dir.rglob('*') if f.is_file())
            size_gb = size_bytes / (1024 ** 3)
            
            # Extract model information
            architecture = config.get("architectures", ["Unknown"])[0] if config.get("architectures") else "Unknown"
            context_length = config.get("max_position_embeddings") or config.get("n_positions")
            model_type = config.get("model_type", "unknown")
            
            return DetectedModel(
                name=model_dir.name,
                path=model_dir,
                backend=ModelBackend.TRANSFORMERS,
                model_type=model_type,
                size_gb=round(size_gb, 1),
                context_length=context_length,
                architecture=architecture,
                description=f"Transformers {architecture} model ({size_gb:.1f}GB)",
                metadata=config
            )
            
        except Exception as e:
            self.logger.error(f"Failed to detect Transformers model {model_dir}: {e}")
            return None
    
    def _extract_gguf_metadata(self, filename: str) -> Dict:
        """Extract metadata from GGUF filename patterns.
        
        Args:
            filename: GGUF filename without extension
            
        Returns:
            Dictionary of extracted metadata
        """
        metadata = {}
        filename_lower = filename.lower()
        
        # Common architecture patterns
        if "llama" in filename_lower:
            metadata["architecture"] = "Llama"
        elif "mistral" in filename_lower:
            metadata["architecture"] = "Mistral"
        elif "phi" in filename_lower:
            metadata["architecture"] = "Phi"
        elif "gemma" in filename_lower:
            metadata["architecture"] = "Gemma"
        elif "qwen" in filename_lower:
            metadata["architecture"] = "Qwen"
        
        # Context length patterns
        if "128k" in filename_lower:
            metadata["context_length"] = 131072
        elif "32k" in filename_lower:
            metadata["context_length"] = 32768
        elif "16k" in filename_lower:
            metadata["context_length"] = 16384
        elif "8k" in filename_lower:
            metadata["context_length"] = 8192
        elif "4k" in filename_lower:
            metadata["context_length"] = 4096
        
        # Model size patterns
        if "7b" in filename_lower:
            metadata["parameters"] = 7_000_000_000
        elif "13b" in filename_lower:
            metadata["parameters"] = 13_000_000_000
        elif "70b" in filename_lower:
            metadata["parameters"] = 70_000_000_000
        elif "3b" in filename_lower:
            metadata["parameters"] = 3_000_000_000
        
        # Quantization patterns
        if "q4_0" in filename_lower:
            metadata["quantization"] = "Q4_0"
        elif "q4_1" in filename_lower:
            metadata["quantization"] = "Q4_1"
        elif "q5_0" in filename_lower:
            metadata["quantization"] = "Q5_0"
        elif "q5_1" in filename_lower:
            metadata["quantization"] = "Q5_1"
        elif "q8_0" in filename_lower:
            metadata["quantization"] = "Q8_0"
        elif "f16" in filename_lower:
            metadata["quantization"] = "F16"
        elif "f32" in filename_lower:
            metadata["quantization"] = "F32"
        
        return metadata
    
    def detect_model_type(self, model_path: Path) -> Tuple[ModelBackend, str]:
        """Detect the type of a specific model.

        Args:
            model_path: Path to model file or directory

        Returns:
            Tuple of (backend, model_type)
        """
        # Check if it's a GGUF single file model
        if model_path.is_file() and model_path.suffix.lower() == ".gguf":
            return ModelBackend.GGUF, "gguf"

        # Check if it's a directory with model files (Transformers format)
        if model_path.is_dir():
            config_file = model_path / "config.json"
            if config_file.exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    model_type = config.get("model_type", "transformers")
                    return ModelBackend.TRANSFORMERS, model_type
                except Exception:
                    pass

            # Check for other common Transformers files
            transformers_files = [
                "pytorch_model.bin",
                "model.safetensors",
                "pytorch_model.bin.index.json",
                "model.safetensors.index.json",
                "tokenizer.json",
                "tokenizer_config.json"
            ]

            if any((model_path / file).exists() for file in transformers_files):
                return ModelBackend.TRANSFORMERS, "transformers"

        # Default fallback
        return ModelBackend.AUTO, "unknown"
    
    def create_model_info(self, detected_model: DetectedModel) -> ModelInfo:
        """Create a ModelInfo object from a DetectedModel.
        
        Args:
            detected_model: Detected model information
            
        Returns:
            ModelInfo object
        """
        return ModelInfo(
            name=detected_model.name,
            backend=detected_model.backend,
            status=ModelStatus.AVAILABLE,
            size_gb=detected_model.size_gb,
            context_length=detected_model.context_length,
            description=detected_model.description,
            architecture=detected_model.architecture,
            parameters=detected_model.metadata.get("parameters") if detected_model.metadata else None,
            metadata=detected_model.metadata
        )


# Global detector instance
_model_detector: Optional[ModelDetector] = None


def get_model_detector() -> ModelDetector:
    """Get the global model detector instance."""
    global _model_detector
    if _model_detector is None:
        _model_detector = ModelDetector()
    return _model_detector
