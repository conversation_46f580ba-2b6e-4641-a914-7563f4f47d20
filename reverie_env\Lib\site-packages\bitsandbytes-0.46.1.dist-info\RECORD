bitsandbytes-0.46.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
bitsandbytes-0.46.1.dist-info/METADATA,sha256=_fTzr1qJUUpZjQXrjwfcE_z-f0fLBbiSuUpObKfxlPI,10938
bitsandbytes-0.46.1.dist-info/RECORD,,
bitsandbytes-0.46.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes-0.46.1.dist-info/WHEEL,sha256=BlXG8Qkq_mCTKpY-toCToAQAmGPE73xawTOhla8_4pE,98
bitsandbytes-0.46.1.dist-info/licenses/LICENSE,sha256=8IqUv7C9wdfxy1m62vD4xVGxDbhxGFYi2_H9A2r_qcs,1107
bitsandbytes-0.46.1.dist-info/licenses/NOTICE.md,sha256=OpIf_XSa2lQICqX1wlMfagqddG9fsg_O4voC0qsvgSo,268
bitsandbytes-0.46.1.dist-info/top_level.txt,sha256=bK-Zzu-JyIIh4njm8jTYcbuqX-Z80XTcDal4lXCG0-M,13
bitsandbytes/__init__.py,sha256=pk55t_yWzqHVuCLxfw_mtVPLComeh_v1BXMbYEkBamM,1882
bitsandbytes/__main__.py,sha256=_ObgXmW-NG395jj_oP86gSOw2LgqMtrGBmpjZOiRcuY,94
bitsandbytes/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/__pycache__/__main__.cpython-310.pyc,,
bitsandbytes/__pycache__/_ops.cpython-310.pyc,,
bitsandbytes/__pycache__/cextension.cpython-310.pyc,,
bitsandbytes/__pycache__/consts.cpython-310.pyc,,
bitsandbytes/__pycache__/cuda_specs.cpython-310.pyc,,
bitsandbytes/__pycache__/functional.cpython-310.pyc,,
bitsandbytes/__pycache__/utils.cpython-310.pyc,,
bitsandbytes/_ops.py,sha256=CA9NxA_sP8zU8ZKA03rKbL1fc-IIzyTd1XYERN9zygg,11811
bitsandbytes/autograd/__init__.py,sha256=OeUeF5-AE6e2Y99DYsWDb20dwvot38GY2xpJUHUrG3Q,68
bitsandbytes/autograd/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/autograd/__pycache__/_functions.cpython-310.pyc,,
bitsandbytes/autograd/_functions.py,sha256=NHG5Gv_fI5w4mRUO51nw7GyofCxHGthIgd7beV0DLKU,14506
bitsandbytes/backends/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/backends/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/backends/cpu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/backends/cpu/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/backends/cpu/__pycache__/ops.cpython-310.pyc,,
bitsandbytes/backends/cpu/ops.py,sha256=0ZQEJi9xtOIMGd-rgncSfjUuJWvDQ7i_LQOfBNLiBz0,5982
bitsandbytes/backends/cuda/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/backends/cuda/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/backends/cuda/__pycache__/ops.cpython-310.pyc,,
bitsandbytes/backends/cuda/ops.py,sha256=H98rK3t_ejjezw8VDqIbghEFQbpqgp9vzb3aYQOlvZY,18288
bitsandbytes/backends/default/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/backends/default/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/backends/default/__pycache__/ops.cpython-310.pyc,,
bitsandbytes/backends/default/ops.py,sha256=7XsvaHrWA4VeT0hb5KwG6aKwbpQNqJGnZTqhV8lS2Jo,4782
bitsandbytes/cextension.py,sha256=Uqrd-wHs-RaWT3bYiMdK3KP6F6gZZ_0Sy2-yjNpPFi0,12816
bitsandbytes/consts.py,sha256=hhASlIvnZUQd4XXo1wlQ86KpSfVSUCW91F7-wVjfjlk,392
bitsandbytes/cuda_specs.py,sha256=aBpu1PdefKQTpVWYL9a-QTERAQ6-5GsUp7fMtVgFZ6M,2172
bitsandbytes/diagnostics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/diagnostics/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/diagnostics/__pycache__/cuda.cpython-310.pyc,,
bitsandbytes/diagnostics/__pycache__/main.cpython-310.pyc,,
bitsandbytes/diagnostics/__pycache__/utils.cpython-310.pyc,,
bitsandbytes/diagnostics/cuda.py,sha256=LoBZjx96k3YQMEYWaeRB0s6wdxcrp-5owZCmxqe62ZQ,6097
bitsandbytes/diagnostics/main.py,sha256=zgdr1GOArV9BYTQJmaUa6mMhGC1v9fnxFvwINFVcg6Q,3396
bitsandbytes/diagnostics/utils.py,sha256=moc7UuWYEB56YDXIsWj-8okDElRaHjcxXl5Tv0iNTdc,296
bitsandbytes/functional.py,sha256=9wCiqN-0IrKcAOuzHkOU7y6tFI3W2VLNwGDCcvOHF10,86768
bitsandbytes/libbitsandbytes_cpu.dll,sha256=eGlJRVD9WEHgJOLU563MaTgOshdW7sqcUKSl1M8hXAs,37376
bitsandbytes/libbitsandbytes_cuda118.dll,sha256=2acH-Hlw46yfAlLAll47VFnRycTQLx6d876ouYmKCKg,25810944
bitsandbytes/libbitsandbytes_cuda120.dll,sha256=jKWuEdXHp3Cdvie7hH5qxjpU6wEBLqNERYi0iYvf6as,25069056
bitsandbytes/libbitsandbytes_cuda121.dll,sha256=L5dK-rX1C9VIlecquHe2WJNbAl3z2iSoWa62GWWLa1U,25079296
bitsandbytes/libbitsandbytes_cuda122.dll,sha256=w77Nk9C9JYQZGmGoSfR96xj-a627popAMVl9dKdyybo,25089024
bitsandbytes/libbitsandbytes_cuda123.dll,sha256=eUfHXwvUcrv8AjHwpFG46I-Gr1foyCDwAGrcFWhjSns,25097216
bitsandbytes/libbitsandbytes_cuda124.dll,sha256=jlOErP4swRvOhgFzIClHT8z1wG-HI44eGK5wIN9Nqz0,24335360
bitsandbytes/libbitsandbytes_cuda125.dll,sha256=EEGnuotSekb9161j-TgKVYnRVAATD7qzk4VmVvXd9gE,24396288
bitsandbytes/libbitsandbytes_cuda126.dll,sha256=OSlru2Hd_AWHiwTCBlQ5EEAiAGVLHwTRVXymrMMd42Y,24421888
bitsandbytes/libbitsandbytes_cuda128.dll,sha256=UtXjCQELLXzwvLbCPznsnFOWwsAtke9e-QeX3T_Dw-w,20594176
bitsandbytes/libbitsandbytes_cuda129.dll,sha256=uzDF6BrhSDbH9Y8X-8odJWt4jF_N7CNBmU3AFtDjrRE,20839936
bitsandbytes/nn/__init__.py,sha256=VpgW4WzMXZ2KO_1aNS5K7-0ZMoXQctU064hISBAzubU,623
bitsandbytes/nn/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/nn/__pycache__/modules.cpython-310.pyc,,
bitsandbytes/nn/__pycache__/triton_based_modules.cpython-310.pyc,,
bitsandbytes/nn/modules.py,sha256=ygwOBN7g3JDbbXjVGr62DmzrsTQ_XX79OMn5qabex80,38870
bitsandbytes/nn/triton_based_modules.py,sha256=nHgsatAx6SDjF3T9WT0-gc1wyTpFkMxt5Ioxb-n6bno,10082
bitsandbytes/optim/__init__.py,sha256=AvB75Z18WaU0EfmE5tH73CwLQxyhLFznOc2VZ97Ovcw,903
bitsandbytes/optim/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/adagrad.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/adam.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/adamw.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/ademamix.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/lamb.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/lars.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/lion.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/optimizer.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/rmsprop.cpython-310.pyc,,
bitsandbytes/optim/__pycache__/sgd.cpython-310.pyc,,
bitsandbytes/optim/adagrad.py,sha256=bHW6e66lQv9DBU2QVaTBY--3HAItkrvCZ-oi50bLyc0,8122
bitsandbytes/optim/adam.py,sha256=_bgegLIQRk7ttiFo5QcTMWfhA8muWRmyQWALSXhaz0k,16316
bitsandbytes/optim/adamw.py,sha256=s0lxvbrKrsWSMnSFW-f_XEmHMD2ZuV67aL62FuC7kUM,15924
bitsandbytes/optim/ademamix.py,sha256=L3KnzsFS0drgnADaoczJtOxD9ffaY8gaU3aozq8aQSs,13447
bitsandbytes/optim/lamb.py,sha256=PrxexS_YODc4r3ev5ii4AxTMPqmKAS6ESag0GvBXRds,8161
bitsandbytes/optim/lars.py,sha256=4rmMYBz_5CzlJZboT8XqLrt7s6qLu_54FWY7lyJn2gY,9667
bitsandbytes/optim/lion.py,sha256=dujq4h6nONJHpKAgWCYNJy8O8LUSeruJ7kLD8kFGF0Q,11931
bitsandbytes/optim/optimizer.py,sha256=pU-IQ1m5tqOqNUHJgPI_T4Ssm7hr_hQsQIJOuNK42KU,31036
bitsandbytes/optim/rmsprop.py,sha256=ewxuwdPQMuL_gRSGEAAlKHrBMPpjR37BEyFFJbcDcbo,7982
bitsandbytes/optim/sgd.py,sha256=YIdIlQRbxWqP-HvV4dJ_pYXeTId1IrACefpc1z0IHgw,6637
bitsandbytes/research/__init__.py,sha256=jSLMzRcp8QOd1VbiQwcOFrQH0fl1pfBxt23c-GxxWmo,125
bitsandbytes/research/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/research/autograd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/research/autograd/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/research/autograd/__pycache__/_functions.cpython-310.pyc,,
bitsandbytes/research/autograd/_functions.py,sha256=agApMsEopZP-IvlYkJ7_IChN97FVXHc2kq8Wr-ZPgBI,14744
bitsandbytes/research/nn/__init__.py,sha256=_93TgkJ0CiQHJ3DL2VWoIqLE8urYCmXMNymVQiJSh4E,54
bitsandbytes/research/nn/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/research/nn/__pycache__/modules.cpython-310.pyc,,
bitsandbytes/research/nn/modules.py,sha256=13EgrXiYCuDbRewxTLvD7Bl-ltnkMN_8Tbm2Qx3MleA,2404
bitsandbytes/triton/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
bitsandbytes/triton/__pycache__/__init__.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/dequantize_rowwise.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_mixed_dequantize.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/int8_matmul_rowwise_dequantize.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/matmul_perf_model.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/quantize_columnwise_and_transpose.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/quantize_global.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/quantize_rowwise.cpython-310.pyc,,
bitsandbytes/triton/__pycache__/triton_utils.cpython-310.pyc,,
bitsandbytes/triton/dequantize_rowwise.py,sha256=YDVUsbRiRrmvH9yM-KqTbgfLt0VUounuOUiU2XlQB84,2104
bitsandbytes/triton/int8_matmul_mixed_dequantize.py,sha256=pfZna4Lg0nnbpFD2jPMHolobTcfCskcFf0FCTTkpiVM,8969
bitsandbytes/triton/int8_matmul_rowwise_dequantize.py,sha256=n38SCZ6Pt2GpHCJlUKjzKQEjJVuvUKLSTg1ORNW2LOE,8954
bitsandbytes/triton/matmul_perf_model.py,sha256=6Trkmouf3472jEcE8Zq2Nw1Aq9NyhOSpaEJhiainfcw,7433
bitsandbytes/triton/quantize_columnwise_and_transpose.py,sha256=jwbnRyjWRbD403CKxPOzF0JIzAmNcUandSq9AZaqqvw,2673
bitsandbytes/triton/quantize_global.py,sha256=L-nOgJoIv2sbiThisHBcHSzNiW4VkHlqXYdidE9TPws,4045
bitsandbytes/triton/quantize_rowwise.py,sha256=oaZ8xRSEFBc3bA-WrhHUwhQ7UIDnH6bEX0MzeeAbRJ4,2242
bitsandbytes/triton/triton_utils.py,sha256=95mMuH7SYNHz8EjcvWaKMN04FXbyOzN6_vD_YCRC17w,350
bitsandbytes/utils.py,sha256=OvAhr-oLgWobMi86YbGM5DlInO9Mtvrr3WthCYHAfpM,7006
