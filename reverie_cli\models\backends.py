"""
Model backend implementations.
"""

import asyncio
import subprocess
import json
import tempfile
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List, AsyncGenerator
from pathlib import Path

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import ModelError, ModelLoadError, ModelInferenceError
from reverie_cli.models.info import ModelInfo, ModelStatus, ModelBackend


logger = get_logger("backends")


class BaseModelBackend(ABC):
    """Base class for model backends."""
    
    def __init__(self, model_info: ModelInfo):
        self.model_info = model_info
        self.model = None
        self.tokenizer = None
        self.device = None
        self.logger = get_logger(f"backend.{self.backend_name}")
    
    @property
    @abstractmethod
    def backend_name(self) -> str:
        """Backend name."""
        pass
    
    @abstractmethod
    async def load_model(
        self, 
        device: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Load the model."""
        pass
    
    @abstractmethod
    async def unload_model(self) -> bool:
        """Unload the model."""
        pass
    
    @abstractmethod
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate text from prompt."""
        pass
    
    @abstractmethod
    async def generate_stream(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate text from prompt with streaming."""
        pass
    
    def is_loaded(self) -> bool:
        """Check if model is loaded."""
        return self.model is not None
    
    def get_memory_usage(self) -> Optional[float]:
        """Get memory usage in GB."""
        # TODO: Implement actual memory usage calculation
        return None


class MockBackend(BaseModelBackend):
    """Mock backend for testing and demonstration."""
    
    @property
    def backend_name(self) -> str:
        return "mock"
    
    async def load_model(
        self, 
        device: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Load the model (mock implementation)."""
        self.logger.info(f"Loading model {self.model_info.name} with mock backend")
        
        # Simulate loading time
        await asyncio.sleep(1)
        
        self.model = "mock_model"
        self.tokenizer = "mock_tokenizer"
        self.device = device or "cpu"
        
        self.model_info.status = ModelStatus.LOADED
        self.model_info.device = self.device
        
        self.logger.info(f"Model {self.model_info.name} loaded successfully")
        return True
    
    async def unload_model(self) -> bool:
        """Unload the model."""
        self.logger.info(f"Unloading model {self.model_info.name}")
        
        self.model = None
        self.tokenizer = None
        self.device = None
        
        self.model_info.status = ModelStatus.AVAILABLE
        self.model_info.device = None
        
        return True
    
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate text from prompt."""
        if not self.is_loaded():
            raise ModelInferenceError("Model not loaded")
        
        self.logger.info(f"Generating response for prompt: {prompt[:50]}...")
        
        # Simulate generation time
        await asyncio.sleep(0.5)
        
        # Mock response based on prompt content
        if "code" in prompt.lower():
            return """Here's a Python example:

```python
def hello_world():
    print("Hello, World!")
    return "Success"

# Call the function
result = hello_world()
```

This function demonstrates basic Python syntax and returns a success message."""
        
        elif "help" in prompt.lower():
            return """I'm Reverie CLI, an AI-native code development assistant. I can help you with:

- Writing and reviewing code
- Explaining programming concepts
- Debugging and troubleshooting
- Project architecture and design
- Documentation and testing

What would you like to work on today?"""
        
        else:
            return f"I understand your request: '{prompt}'. This is a mock response from the {self.model_info.name} model. How can I help you with your coding project?"
    
    async def generate_stream(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate text from prompt with streaming."""
        if not self.is_loaded():
            raise ModelInferenceError("Model not loaded")
        
        # Generate full response first
        response = await self.generate(prompt, max_tokens, temperature, **kwargs)
        words = response.split()
        
        # Stream word by word
        for word in words:
            yield word + " "
            await asyncio.sleep(0.05)  # Simulate streaming delay


class TransformersBackend(BaseModelBackend):
    """Transformers backend implementation."""
    
    @property
    def backend_name(self) -> str:
        return "transformers"
    
    async def load_model(
        self, 
        device: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Load the model using Transformers."""
        try:
            # TODO: Implement actual Transformers loading
            self.logger.info(f"Loading {self.model_info.name} with Transformers backend")
            
            # For now, use mock implementation
            mock_backend = MockBackend(self.model_info)
            return await mock_backend.load_model(device, **kwargs)
            
        except Exception as e:
            raise ModelLoadError(f"Failed to load model with Transformers: {e}")
    
    async def unload_model(self) -> bool:
        """Unload the model."""
        # TODO: Implement actual unloading
        mock_backend = MockBackend(self.model_info)
        return await mock_backend.unload_model()
    
    async def generate(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate text from prompt."""
        # TODO: Implement actual generation
        mock_backend = MockBackend(self.model_info)
        mock_backend.model = self.model  # Copy state
        return await mock_backend.generate(prompt, max_tokens, temperature, **kwargs)
    
    async def generate_stream(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate text from prompt with streaming."""
        # TODO: Implement actual streaming
        mock_backend = MockBackend(self.model_info)
        mock_backend.model = self.model  # Copy state
        async for chunk in mock_backend.generate_stream(prompt, max_tokens, temperature, **kwargs):
            yield chunk


class GGUFBackend(BaseModelBackend):
    """GGUF backend implementation using llama.cpp server or llama-cpp-python."""

    def __init__(self, model_info: ModelInfo):
        super().__init__(model_info)
        self.process = None
        self.model_path = None
        self.server_port = None
        self.temp_dir = None
        self.llama_model = None  # For llama-cpp-python
        self.backend_type = None  # "server" or "python"

    @property
    def backend_name(self) -> str:
        return "gguf"

    async def load_model(
        self,
        device: Optional[str] = None,
        **kwargs
    ) -> bool:
        """Load the GGUF model using llama.cpp server or llama-cpp-python."""
        # Find the model file first
        self.model_path = self._find_model_file()
        if not self.model_path:
            raise ModelLoadError(f"GGUF model file not found for {self.model_info.name}")

        # Try llama.cpp server first, then fallback to llama-cpp-python
        try:
            success = await self._load_with_server(device, **kwargs)
            if success:
                self.backend_type = "server"
                return True
        except Exception as e:
            self.logger.warning(f"Failed to load with llama.cpp server: {e}")
            self.logger.info("Trying llama-cpp-python as fallback...")

        # Fallback to llama-cpp-python
        try:
            success = await self._load_with_python(device, **kwargs)
            if success:
                self.backend_type = "python"
                return True
        except Exception as e:
            self.logger.error(f"Failed to load with llama-cpp-python: {e}")

        raise ModelLoadError(f"Failed to load GGUF model with both llama.cpp server and llama-cpp-python")

    async def _load_with_server(self, device: Optional[str] = None, **kwargs) -> bool:
        """Load model using llama.cpp server."""
        from reverie_cli.models.llama_cpp_manager import get_llama_cpp_manager

        llama_manager = get_llama_cpp_manager()

        # Ensure llama.cpp is installed
        if not llama_manager.is_installed():
            self.logger.info("llama.cpp not found, downloading...")
            success = await llama_manager.download_and_install()
            if not success:
                raise ModelLoadError("Failed to install llama.cpp")

        # Get server executable
        server_exe = llama_manager.get_executable_path("server")
        if not server_exe:
            raise ModelLoadError("llama-server.exe not found")

        # Find available port
        self.server_port = self._find_available_port()

        # Start llama.cpp server
        cmd = [
            str(server_exe),
            "-m", str(self.model_path),
            "--port", str(self.server_port),
            "--host", "127.0.0.1",
            "-c", str(kwargs.get("context_length", 2048)),
            "-t", str(kwargs.get("threads", 4)),
        ]

        # Add additional parameters
        if kwargs.get("gpu_layers"):
            cmd.extend(["-ngl", str(kwargs["gpu_layers"])])

        self.logger.info(f"Starting llama.cpp server: {' '.join(cmd)}")

        # Create temporary directory for logs
        self.temp_dir = Path(tempfile.mkdtemp(prefix="reverie_gguf_"))
        log_file = self.temp_dir / "server.log"

        # Start the process
        with open(log_file, "w") as f:
            self.process = subprocess.Popen(
                cmd,
                stdout=f,
                stderr=subprocess.STDOUT,
                cwd=llama_manager.loader_dir
            )

        # Wait for server to start
        await self._wait_for_server_start()

        self.model = f"llama_cpp_server:{self.server_port}"
        self.device = device or "cpu"

        self.logger.info(f"GGUF model {self.model_info.name} loaded successfully with llama.cpp server on port {self.server_port}")
        return True

    async def _load_with_python(self, device: Optional[str] = None, **kwargs) -> bool:
        """Load model using llama-cpp-python."""
        try:
            from llama_cpp import Llama
        except ImportError:
            raise ModelLoadError("llama-cpp-python not installed. Install with: pip install llama-cpp-python")

        self.logger.info(f"Loading GGUF model with llama-cpp-python: {self.model_path}")

        # Configure parameters
        model_kwargs = {
            "model_path": str(self.model_path),
            "n_ctx": kwargs.get("context_length", 2048),
            "n_threads": kwargs.get("threads", 4),
            "verbose": False,
        }

        # Add GPU layers if specified
        if kwargs.get("gpu_layers"):
            model_kwargs["n_gpu_layers"] = kwargs["gpu_layers"]

        # Load the model
        self.llama_model = Llama(**model_kwargs)

        self.model = f"llama_cpp_python:{self.model_path.name}"
        self.device = device or "cpu"

        self.logger.info(f"GGUF model {self.model_info.name} loaded successfully with llama-cpp-python")
        return True

    async def unload_model(self) -> bool:
        """Unload the GGUF model."""
        return await self._cleanup()

    async def _cleanup(self) -> bool:
        """Cleanup resources."""
        try:
            # Cleanup server process if using server backend
            if self.backend_type == "server" and self.process:
                self.process.terminate()
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
                self.process = None

            # Cleanup llama-cpp-python model if using python backend
            if self.backend_type == "python" and self.llama_model:
                # llama-cpp-python models are automatically cleaned up by garbage collection
                self.llama_model = None

            # Cleanup temporary directory
            if self.temp_dir and self.temp_dir.exists():
                import shutil
                shutil.rmtree(self.temp_dir)
                self.temp_dir = None

            self.model = None
            self.model_path = None
            self.server_port = None
            self.backend_type = None

            self.logger.info(f"GGUF model {self.model_info.name} unloaded successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to cleanup GGUF model: {e}")
            return False

    def _find_model_file(self) -> Optional[Path]:
        """Find the GGUF model file."""
        models_dir = Path.cwd() / "models" / "llm"
        gguf_dir = models_dir / "gguf"

        # Try exact name match in gguf subdirectory
        exact_path = gguf_dir / f"{self.model_info.name}.gguf"
        if exact_path.exists():
            return exact_path

        # Try exact name match in root models directory
        root_exact_path = models_dir / f"{self.model_info.name}.gguf"
        if root_exact_path.exists():
            return root_exact_path

        # Try name without extension in gguf subdirectory
        name_without_ext = self.model_info.name.replace(".gguf", "")
        alt_path = gguf_dir / f"{name_without_ext}.gguf"
        if alt_path.exists():
            return alt_path

        # Try name without extension in root directory
        root_alt_path = models_dir / f"{name_without_ext}.gguf"
        if root_alt_path.exists():
            return root_alt_path

        # Search for files containing the model name in gguf subdirectory
        if gguf_dir.exists():
            for gguf_file in gguf_dir.glob("*.gguf"):
                if name_without_ext.lower() in gguf_file.stem.lower():
                    return gguf_file

        # Search for files containing the model name in root directory
        for gguf_file in models_dir.glob("*.gguf"):
            if name_without_ext.lower() in gguf_file.stem.lower():
                return gguf_file

        return None

    def _find_available_port(self, start_port: int = 8080) -> int:
        """Find an available port for the server."""
        import socket

        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue

        raise ModelLoadError("No available port found for llama.cpp server")

    async def _wait_for_server_start(self, timeout: int = 30) -> bool:
        """Wait for the llama.cpp server to start."""
        import httpx

        url = f"http://127.0.0.1:{self.server_port}/health"

        for _ in range(timeout):
            try:
                async with httpx.AsyncClient() as client:
                    response = await client.get(url, timeout=1.0)
                    if response.status_code == 200:
                        return True
            except:
                pass

            await asyncio.sleep(1)

        raise ModelLoadError("llama.cpp server failed to start within timeout")

    async def generate(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate text from prompt using llama.cpp server or llama-cpp-python."""
        if not self.is_loaded():
            raise ModelInferenceError("GGUF model not loaded")

        if self.backend_type == "server":
            return await self._generate_with_server(prompt, max_tokens, temperature, **kwargs)
        elif self.backend_type == "python":
            return await self._generate_with_python(prompt, max_tokens, temperature, **kwargs)
        else:
            raise ModelInferenceError("Unknown backend type")

    async def _generate_with_server(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate text using llama.cpp server."""
        try:
            import httpx

            url = f"http://127.0.0.1:{self.server_port}/completion"

            payload = {
                "prompt": prompt,
                "n_predict": max_tokens,
                "temperature": temperature,
                "top_k": kwargs.get("top_k", 40),
                "top_p": kwargs.get("top_p", 0.9),
                "repeat_penalty": kwargs.get("repeat_penalty", 1.1),
                "stream": False
            }

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(url, json=payload)
                response.raise_for_status()

                result = response.json()
                return result.get("content", "")

        except Exception as e:
            self.logger.error(f"GGUF server generation failed: {e}")
            raise ModelInferenceError(f"GGUF server generation failed: {e}")

    async def _generate_with_python(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate text using llama-cpp-python."""
        try:
            if not self.llama_model:
                raise ModelInferenceError("llama-cpp-python model not loaded")

            # Run generation in thread pool to avoid blocking
            import asyncio
            import functools

            def _generate():
                output = self.llama_model(
                    prompt,
                    max_tokens=max_tokens,
                    temperature=temperature,
                    top_k=kwargs.get("top_k", 40),
                    top_p=kwargs.get("top_p", 0.9),
                    repeat_penalty=kwargs.get("repeat_penalty", 1.1),
                    echo=False,
                )
                return output["choices"][0]["text"]

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _generate)
            return result

        except Exception as e:
            self.logger.error(f"GGUF python generation failed: {e}")
            raise ModelInferenceError(f"GGUF python generation failed: {e}")

    async def generate_stream(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """Generate text from prompt with streaming using llama.cpp server."""
        if not self.is_loaded():
            raise ModelInferenceError("GGUF model not loaded")

        try:
            import httpx

            url = f"http://127.0.0.1:{self.server_port}/completion"

            payload = {
                "prompt": prompt,
                "n_predict": max_tokens,
                "temperature": temperature,
                "top_k": kwargs.get("top_k", 40),
                "top_p": kwargs.get("top_p", 0.9),
                "repeat_penalty": kwargs.get("repeat_penalty", 1.1),
                "stream": True
            }

            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream("POST", url, json=payload) as response:
                    response.raise_for_status()

                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            try:
                                data = json.loads(line[6:])  # Remove "data: " prefix
                                if "content" in data:
                                    yield data["content"]
                                if data.get("stop", False):
                                    break
                            except json.JSONDecodeError:
                                continue

        except Exception as e:
            self.logger.error(f"GGUF streaming generation failed: {e}")
            raise ModelInferenceError(f"GGUF streaming generation failed: {e}")


# Backend registry
BACKEND_REGISTRY = {
    ModelBackend.TRANSFORMERS: TransformersBackend,
    ModelBackend.GGUF: GGUFBackend,
    ModelBackend.VLLM: MockBackend,  # TODO: Implement vLLM backend
    ModelBackend.TENSORFLOW: MockBackend,  # TODO: Implement TensorFlow backend
}


def get_backend_class(backend: ModelBackend) -> type:
    """Get backend class by type."""
    return BACKEND_REGISTRY.get(backend, MockBackend)


def create_backend(model_info: ModelInfo, backend: Optional[ModelBackend] = None) -> BaseModelBackend:
    """Create a backend instance."""
    if backend is None:
        backend = model_info.backend
    
    backend_class = get_backend_class(backend)
    return backend_class(model_info)
