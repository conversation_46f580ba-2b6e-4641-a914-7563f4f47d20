# Local LLM Models Directory

This directory contains locally stored language models for Reverie CLI.

## Directory Structure

```
models/llm/
├── README.md                    # This file
├── transformers/               # Transformers models (HuggingFace format)
│   ├── model_name/
│   │   ├── config.json
│   │   ├── pytorch_model.bin
│   │   └── tokenizer.json
│   └── ...
├── gguf/                       # GGUF models (llama.cpp format)
│   ├── model_name.gguf
│   └── ...
├── model_name.gguf             # GGUF models can also be in root
└── model_directory/            # Transformers models can also be in root
    ├── config.json
    └── ...

loader/llama.cpp/               # llama.cpp binaries (separate directory)
├── llama-cli.exe
├── llama-server.exe
└── version.txt
```

## Supported Model Formats

### 1. Transformers Models
- Format: HuggingFace Transformers format
- Backend: `transformers`
- Location: `models/llm/transformers/`
- Supported architectures: Llama, Mistral, Phi, Gemma, etc.

### 2. GGUF Models
- Format: GGUF (GPT-Generated Unified Format)
- Backend: `llama.cpp`
- Location: `models/llm/gguf/` or `models/llm/` (root)
- Advantages: Optimized for CPU inference, smaller memory footprint
- Single file format: Easy to manage and distribute

## Model Management

### Automatic Model Type Detection
Reverie CLI automatically detects model types based on:
- File extensions (`.gguf` for GGUF models)
- Directory structure (HuggingFace format detection)
- Model configuration files

### Loading Models
Models are loaded from this local directory only. The system will:
1. Scan the `models/llm/` directory for available models
2. Auto-detect model format and backend
3. Load using the appropriate backend (transformers or llama.cpp)

### Adding New Models

#### For Transformers Models:
1. Create a subdirectory in `models/llm/transformers/` or `models/llm/`
2. Place model files (config.json, model weights, tokenizer files)
3. Use `models scan` command to detect the new model

#### For GGUF Models:
1. Place `.gguf` files in `models/llm/gguf/` or `models/llm/`
2. Ensure llama.cpp binaries are available in `loader/llama.cpp/`
3. Use `models scan` command to detect the new model

## llama.cpp Integration

### Loader Download Script
Use the `loader-Downloader.bat` script to download and install llama.cpp binaries:
- Available versions: b6106 with CUDA 12.4, CPU, or CUDA 11.8 support
- Installation location: `loader/llama.cpp/`
- Automatic extraction and cleanup

### Manual Installation
You can manually download llama.cpp binaries to the `loader/llama.cpp/` directory:
- Required files: `llama-cli.exe`, `llama-server.exe`
- Optional: Additional DLL files for GPU support

## Important Notes

⚠️ **Local Models Only**: Reverie CLI only uses models from this local directory. It does not download or use models from default cache directories.

⚠️ **Storage Requirements**: Ensure sufficient disk space as language models can be several GB in size.

⚠️ **Performance**: GGUF models generally offer better CPU performance and lower memory usage compared to Transformers models.

## Commands

Use the following commands in Reverie CLI to manage models:

```bash
# List available models
models list

# Load a specific model
models load <model_name>

# Download llama.cpp binaries (deprecated - use loader-Downloader.bat)
models download-llama-cpp [version]

# Show model information
models info <model_name>

# Unload current model
models unload
```
