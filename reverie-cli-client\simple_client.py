#!/usr/bin/env python3
"""
Simple Reverie CLI Client - Optimized for PyInstaller compilation
"""

import os
import sys
import json
import asyncio
import argparse

# Check for required modules
try:
    import aiohttp
except ImportError:
    print("❌ Error: aiohttp not installed")
    print("Please install with: pip install aiohttp")
    sys.exit(1)


class SimpleReverieClient:
    """Simple Reverie CLI Client."""
    
    def __init__(self, host="localhost", port=8000):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.session = None
        self.running = True
        
    def print_banner(self):
        """Print banner."""
        banner = f"""
██████╗ ███████╗██╗   ██╗███████╗██████╗ ██╗███████╗     ██████╗██╗     ██╗
██╔══██╗██╔════╝██║   ██║██╔════╝██╔══██╗██║██╔════╝    ██╔════╝██║     ██║
██████╔╝█████╗  ██║   ██║█████╗  ██████╔╝██║█████╗      ██║     ██║     ██║
██╔══██╗██╔══╝  ╚██╗ ██╔╝██╔══╝  ██╔══██╗██║██╔══╝      ██║     ██║     ██║
██║  ██║███████╗ ╚████╔╝ ███████╗██║  ██║██║███████╗    ╚██████╗███████╗██║
╚═╝  ╚═╝╚══════╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝╚══════╝     ╚═════╝╚══════╝╚═╝

        AI-Native Development Assistant - Client Mode
        Connected to: {self.base_url}
        """
        print(banner)
    
    async def start_session(self):
        """Start HTTP session."""
        self.session = aiohttp.ClientSession()
    
    async def close_session(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
    
    async def check_connection(self):
        """Check server connection."""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Connected to Reverie CLI Server")
                    print(f"   Status: {data.get('status', 'Unknown')}")
                    return True
                else:
                    print(f"❌ Server error: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    async def api_get(self, endpoint):
        """Make GET request."""
        url = f"{self.base_url}/api/v1/{endpoint.lstrip('/')}"
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error = await response.text()
                    raise Exception(f"API error {response.status}: {error}")
        except Exception as e:
            raise Exception(f"Request failed: {e}")
    
    async def api_post(self, endpoint, data=None):
        """Make POST request."""
        url = f"{self.base_url}/api/v1/{endpoint.lstrip('/')}"
        try:
            async with self.session.post(url, json=data or {}) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error = await response.text()
                    raise Exception(f"API error {response.status}: {error}")
        except Exception as e:
            raise Exception(f"Request failed: {e}")
    
    async def cmd_help(self):
        """Show help."""
        help_text = """
🎯 Reverie CLI Client Commands

📁 File Operations:
  ls [path]     - List directory
  cat <file>    - Show file content
  
🔍 Web & Search:
  search <query> - Web search
  
🤖 AI & Models:
  models        - List models
  status        - Server status
  
⚙️ System:
  help          - Show this help
  exit          - Exit client
        """
        print(help_text)
    
    async def cmd_models(self):
        """List models."""
        try:
            result = await self.api_get("/models")
            print("\n🤖 Available Models")
            print("=" * 40)
            
            models = result.get('models', [])
            if not models:
                print("No models found")
                return
                
            for model in models:
                status = model.get('status', 'unknown')
                icon = "🟢" if status == "loaded" else "⚪" if status == "available" else "🔴"
                print(f"{icon} {model.get('name', 'Unknown')} ({model.get('backend', 'unknown')})")
                
        except Exception as e:
            print(f"❌ Failed to list models: {e}")
    
    async def cmd_status(self):
        """Show status."""
        try:
            result = await self.api_get("/health")
            print("\n⚙️ Server Status")
            print("=" * 40)
            print(f"Service: {result.get('service', 'Unknown')}")
            print(f"Status: {result.get('status', 'Unknown')}")
            print(f"Version: {result.get('version', 'Unknown')}")
        except Exception as e:
            print(f"❌ Failed to get status: {e}")
    
    async def cmd_search(self, query):
        """Web search."""
        if not query:
            print("❌ Please provide a search query")
            return
            
        try:
            result = await self.api_post("/web/search", {
                "query": query,
                "num_results": 5
            })
            
            print(f"\n🔍 Search Results: {query}")
            print("=" * 40)
            
            for i, item in enumerate(result.get('results', []), 1):
                print(f"{i}. {item.get('title', 'No title')}")
                print(f"   {item.get('url', '')}")
                print(f"   {item.get('snippet', '')[:100]}...")
                print()
                
        except Exception as e:
            print(f"❌ Search failed: {e}")
    
    async def cmd_ls(self, path="."):
        """List directory."""
        try:
            result = await self.api_post("/files/list", {"path": path})
            print(f"\n📁 Directory: {path}")
            print("=" * 40)
            
            items = result.get('items', [])
            if not items:
                print("(empty)")
                return
                
            for item in items:
                icon = "📁" if item.get('type') == 'directory' else "📄"
                print(f"{icon} {item.get('name', '')}")
                
        except Exception as e:
            print(f"❌ Failed to list directory: {e}")
    
    async def cmd_cat(self, filepath):
        """Show file content."""
        if not filepath:
            print("❌ Please specify a file")
            return
            
        try:
            result = await self.api_post("/files/read", {"path": filepath})
            print(f"\n📄 File: {filepath}")
            print("=" * 40)
            print(result.get('content', ''))
        except Exception as e:
            print(f"❌ Failed to read file: {e}")
    
    async def process_command(self, line):
        """Process command."""
        if not line.strip():
            return
            
        parts = line.strip().split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if cmd == "help":
            await self.cmd_help()
        elif cmd == "models":
            await self.cmd_models()
        elif cmd == "status":
            await self.cmd_status()
        elif cmd == "search":
            await self.cmd_search(" ".join(args))
        elif cmd == "ls":
            await self.cmd_ls(args[0] if args else ".")
        elif cmd == "cat":
            await self.cmd_cat(args[0] if args else "")
        elif cmd in ["exit", "quit"]:
            self.running = False
        else:
            print(f"❌ Unknown command: {cmd}")
            print("Type 'help' for available commands")
    
    async def run(self):
        """Run interactive client."""
        self.print_banner()
        
        await self.start_session()
        
        try:
            if not await self.check_connection():
                return
            
            print("\n" + "=" * 60)
            print("Type 'help' for commands, 'exit' to quit")
            print("=" * 60)
            
            while self.running:
                try:
                    line = input("\nReverie CLI> ").strip()
                    if line:
                        await self.process_command(line)
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    break
                except EOFError:
                    print("\n👋 Goodbye!")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")
                    
        finally:
            await self.close_session()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Reverie CLI Client")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--command", help="Single command to execute")
    
    args = parser.parse_args()
    
    client = SimpleReverieClient(args.host, args.port)
    
    if args.command:
        await client.start_session()
        try:
            await client.process_command(args.command)
        finally:
            await client.close_session()
    else:
        await client.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
