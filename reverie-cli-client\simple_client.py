#!/usr/bin/env python3
"""
Simple Reverie CLI Client - Optimized for PyInstaller compilation
"""

import os
import sys
import json
import asyncio
import argparse
import ctypes
from ctypes import wintypes

# Check for required modules
try:
    import aiohttp
except ImportError:
    print("❌ Error: aiohttp not installed")
    print("Please install with: pip install aiohttp")
    sys.exit(1)

# Import readline for command completion (with fallback)
try:
    import readline
    import rlcompleter
    READLINE_AVAILABLE = True
except ImportError:
    READLINE_AVAILABLE = False


class CommandCompleter:
    """Command completion handler for readline."""

    def __init__(self):
        """Initialize the completer with available commands."""
        self.commands = [
            'help', 'ls', 'cat', 'search', 'models', 'status',
            'chat', 'ask', 'agent', 'exit', 'quit'
        ]

        # Command options
        self.command_options = {
            'chat': ['--model', '--stream', '--enhanced'],
            'ask': ['--model', '--stream', '--enhanced'],
            'agent': ['--type', '--project'],
            'models': ['load', 'unload', 'current'],
            'ls': [],
            'cat': [],
            'search': [],
            'status': [],
            'help': [],
            'exit': [],
            'quit': []
        }

        # Task types for agent command
        self.agent_task_types = [
            'code', 'analyze', 'search', 'debug', 'review',
            'optimize', 'security', 'general'
        ]

    def complete(self, text, state):
        """Complete command based on current input."""
        try:
            # Get the current line
            line = readline.get_line_buffer()
            words = line.split()

            # Store matches for this completion session
            if not hasattr(self, '_current_matches') or getattr(self, '_last_text', '') != text:
                self._last_text = text

                if not words or (len(words) == 1 and not line.endswith(' ')):
                    # Complete main commands
                    self._current_matches = [cmd for cmd in self.commands if cmd.startswith(text)]
                else:
                    # Complete subcommands or options
                    command = words[0].lower()
                    self._current_matches = self._complete_subcommand(command, words, text, line)

            if state < len(self._current_matches):
                return self._current_matches[state]
            else:
                return None

        except Exception:
            return None

    def _complete_subcommand(self, command: str, words: list, text: str, line: str) -> list:
        """Complete subcommands and options for a specific command."""
        matches = []

        if command == 'models':
            # Complete models subcommands
            if len(words) == 2 and not line.endswith(' '):
                # Completing the subcommand
                subcommands = ['load', 'unload', 'current']
                matches = [sub for sub in subcommands if sub.startswith(text)]

        elif command in ['chat', 'ask']:
            # Complete chat options
            if text.startswith('--'):
                options = ['--model', '--stream', '--enhanced']
                matches = [opt for opt in options if opt.startswith(text)]

        elif command == 'agent':
            # Complete agent options and task types
            if text.startswith('--'):
                options = ['--type', '--project']
                matches = [opt for opt in options if opt.startswith(text)]
            elif '--type' in words:
                # Complete task types after --type
                try:
                    type_index = words.index('--type')
                    if len(words) > type_index + 1:
                        # We're completing the task type
                        matches = [t for t in self.agent_task_types if t.startswith(text)]
                except ValueError:
                    pass

        return matches


class SimpleReverieClient:
    """Simple Reverie CLI Client."""
    
    def __init__(self, host="localhost", port=8000):
        self.host = host
        self.port = port
        self.base_url = f"http://{host}:{port}"
        self.session = None
        self.running = True
        self.completer = CommandCompleter() if READLINE_AVAILABLE else None
    
    def _supports_ansi(self) -> bool:
        """Try to enable ANSI (VT) on Windows; return True if supported."""
        # Respect NO_COLOR if set
        if os.environ.get("NO_COLOR"):
            return False
        # Non-Windows: assume ANSI if TTY
        if os.name != 'nt':
            return sys.stdout.isatty()
        # Windows: try to enable VT processing
        try:
            kernel32 = ctypes.windll.kernel32
            handle = kernel32.GetStdHandle(-11)  # STD_OUTPUT_HANDLE
            mode = wintypes.DWORD()
            if kernel32.GetConsoleMode(handle, ctypes.byref(mode)):
                new_mode = mode.value | 0x0004  # ENABLE_VIRTUAL_TERMINAL_PROCESSING
                kernel32.SetConsoleMode(handle, new_mode)
                return True
            return False
        except Exception:
            # Fallback: some terminals (Windows Terminal) already support ANSI
            return sys.stdout.isatty()

    def _print_gradient_block(self, text: str, start_rgb=(233, 213, 255), end_rgb=(192, 132, 252)) -> None:
        """Print multiline text with left-to-right gentle purple gradient."""
        if not self._supports_ansi():
            print(text)
            return
        lines = text.splitlines()
        if not lines:
            return
        max_len = max((len(line) for line in lines), default=0)
        if max_len <= 1:
            print(text)
            return
        sR, sG, sB = start_rgb
        eR, eG, eB = end_rgb
        for line in lines:
            # Right-pad to align gradient across lines for a smoother effect
            padded = line.ljust(max_len)
            for i, ch in enumerate(padded):
                ratio = i / (max_len - 1)
                r = round(sR + (eR - sR) * ratio)
                g = round(sG + (eG - sG) * ratio)
                b = round(sB + (eB - sB) * ratio)
                sys.stdout.write(f"\033[38;2;{r};{g};{b}m{ch}")
            sys.stdout.write("\033[0m\n")
        
    def print_banner(self):
        """Print banner with a gentle, elegant light-purple gradient title."""
        ascii_art = (
            "\n"
            "██████╗ ███████╗██╗   ██╗███████╗██████╗ ██╗███████╗     ██████╗██╗     ██╗\n"
            "██╔══██╗██╔════╝██║   ██║██╔════╝██╔══██╗██║██╔════╝    ██╔════╝██║     ██║\n"
            "██████╔╝█████╗  ██║   ██║█████╗  ██████╔╝██║█████╗      ██║     ██║     ██║\n"
            "██╔══██╗██╔══╝  ╚██╗ ██╔╝██╔══╝  ██╔══██╗██║██╔══╝      ██║     ██║     ██║\n"
            "██║  ██║███████╗ ╚████╔╝ ███████╗██║  ██║██║███████╗    ╚██████╗███████╗██║\n"
            "╚═╝  ╚═╝╚══════╝  ╚═══╝  ╚══════╝╚═╝  ╚═╝╚═╝╚══════╝     ╚═════╝╚══════╝╚═╝\n"
        )
        # Gentle purple gradient from #E9D5FF to #C084FC
        self._print_gradient_block(ascii_art, (233, 213, 255), (192, 132, 252))
        print(
            f"""
        AI-Native Development Assistant - Client Mode
        Connected to: {self.base_url}
            """
        )
    
    async def start_session(self):
        """Start HTTP session."""
        self.session = aiohttp.ClientSession()
    
    async def close_session(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
    
    async def check_connection(self):
        """Check server connection."""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print("✅ Connected to Reverie CLI Server")
                    print(f"   Status: {data.get('status', 'Unknown')}")
                    return True
                else:
                    print(f"❌ Server error: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False

    def prompt_custom_server(self):
        """Prompt user for custom server URL."""
        print("\n" + "=" * 60)
        print("🔧 Server Connection Configuration")
        print("=" * 60)
        print("Default server connection failed. You can:")
        print("1. Enter a custom server URL")
        print("2. Exit the application")
        print()

        while True:
            choice = input("Enter your choice (1/2): ").strip()
            if choice == "1":
                while True:
                    custom_url = input("Enter custom server URL (e.g., http://*************:8000): ").strip()
                    if custom_url:
                        # Parse the URL to extract host and port
                        try:
                            if not custom_url.startswith(('http://', 'https://')):
                                custom_url = 'http://' + custom_url

                            from urllib.parse import urlparse
                            parsed = urlparse(custom_url)

                            if parsed.hostname:
                                self.host = parsed.hostname
                                self.port = parsed.port or 8000
                                self.base_url = f"http://{self.host}:{self.port}"
                                print(f"✅ Server URL updated to: {self.base_url}")
                                return True
                            else:
                                print("❌ Invalid URL format. Please try again.")
                        except Exception as e:
                            print(f"❌ Error parsing URL: {e}")
                    else:
                        print("❌ Please enter a valid URL.")
            elif choice == "2":
                print("👋 Goodbye!")
                return False
            else:
                print("❌ Invalid choice. Please enter 1 or 2.")
    
    async def api_get(self, endpoint):
        """Make GET request."""
        url = f"{self.base_url}/api/v1/{endpoint.lstrip('/')}"
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error = await response.text()
                    raise Exception(f"API error {response.status}: {error}")
        except Exception as e:
            raise Exception(f"Request failed: {e}")
    
    async def api_post(self, endpoint, data=None):
        """Make POST request."""
        url = f"{self.base_url}/api/v1/{endpoint.lstrip('/')}"
        try:
            async with self.session.post(url, json=data or {}) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error = await response.text()
                    raise Exception(f"API error {response.status}: {error}")
        except Exception as e:
            raise Exception(f"Request failed: {e}")
    
    async def cmd_help(self):
        """Show help."""
        help_text = """
🎯 Reverie CLI Client Commands

📁 File Operations:
  ls [path]              - List directory contents
  cat <file>             - Display file contents

🔍 Web & Search:
  search <query>         - Web search

🤖 AI & Chat:
  chat <message>         - Chat with AI
    --model <name>       - Use specific model
    --stream             - Enable streaming response
    --enhanced           - Use enhanced chat with web search
  ask <question>         - Alias for chat command

🚀 AI Agent:
  agent <task>           - Execute AI agent tasks (auto-selects best approach)
    --type <type>        - Task type (optional: code, analyze, search, debug, review, optimize, security)
    --project <path>     - Project path for context (optional)

🤖 Model Management:
  models                 - List all available models
  models load <name>     - Load a specific model
  models unload          - Unload current model
  models current         - Show current model info

⚙️ System:
  status                 - Show server status
  help                   - Show this help
  exit                   - Exit client

💡 Examples:
  chat "Explain async programming in Python"
  chat "Write a function" --enhanced --model gpt-4
  agent "Create a REST API with FastAPI"
  agent "Analyze this codebase" --project ./src
  agent "Debug memory leak in my application"
  agent "Review code quality" --project ./app
  models load Lucy-128k
  search "Python best practices"
        """
        print(help_text)
    
    async def cmd_models(self, args=None):
        """Enhanced model management."""
        if not args:
            # List all models
            try:
                result = await self.api_get("/models")

                print("\n🤖 Available Models")
                print("=" * 60)

                models = result.get('models', [])
                if not models:
                    print("No models found")
                    return

                current_model = result.get('current_model')

                for model in models:
                    status_icon = {
                        "loaded": "🟢",
                        "available": "⚪",
                        "loading": "🟡",
                        "error": "🔴"
                    }.get(model.get('status'), "❓")

                    name = model.get('name', 'Unknown')
                    backend = model.get('backend', 'Unknown')
                    current_marker = " ← CURRENT" if name == current_model else ""

                    print(f"{status_icon} {name} ({backend}){current_marker}")

                    # Show additional info if available
                    if model.get('size'):
                        print(f"    📏 Size: {model['size']}")
                    if model.get('description'):
                        print(f"    📝 {model['description']}")

                print(f"\n💡 Usage: models load <name> | models unload | models current")

            except Exception as e:
                print(f"❌ Failed to list models: {e}")

        elif args[0] == "load":
            # Load a specific model
            if len(args) < 2:
                print("❌ Please specify a model name")
                print("💡 Usage: models load <model_name>")
                return

            model_name = args[1]
            try:
                print(f"⏳ Loading model: {model_name}")
                result = await self.api_post("/models/load", {
                    "model_name": model_name
                })

                if result.get('success'):
                    print(f"✅ Model '{model_name}' loaded successfully")
                    if result.get('info'):
                        print(f"📋 Info: {result['info']}")
                else:
                    print(f"❌ Failed to load model: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ Failed to load model: {e}")

        elif args[0] == "unload":
            # Unload current model
            try:
                print("⏳ Unloading current model...")
                result = await self.api_post("/models/unload", {})

                if result.get('success'):
                    print("✅ Model unloaded successfully")
                else:
                    print(f"❌ Failed to unload model: {result.get('error', 'Unknown error')}")

            except Exception as e:
                print(f"❌ Failed to unload model: {e}")

        elif args[0] == "current":
            # Show current model info
            try:
                result = await self.api_get("/models/current")

                if result.get('model'):
                    model = result['model']
                    print(f"\n🤖 Current Model")
                    print("=" * 40)
                    print(f"📛 Name: {model.get('name', 'Unknown')}")
                    print(f"🔧 Backend: {model.get('backend', 'Unknown')}")
                    print(f"📊 Status: {model.get('status', 'Unknown')}")
                    if model.get('size'):
                        print(f"📏 Size: {model['size']}")
                    if model.get('loaded_at'):
                        print(f"⏰ Loaded: {model['loaded_at']}")
                else:
                    print("No model currently loaded")

            except Exception as e:
                print(f"❌ Failed to get current model: {e}")

        else:
            print(f"❌ Unknown models command: {args[0]}")
            print("💡 Available commands: load, unload, current")
    
    async def cmd_status(self):
        """Show status."""
        try:
            result = await self.api_get("/health")
            print("\n⚙️ Server Status")
            print("=" * 40)
            print(f"Service: {result.get('service', 'Unknown')}")
            print(f"Status: {result.get('status', 'Unknown')}")
            print(f"Version: {result.get('version', 'Unknown')}")
        except Exception as e:
            print(f"❌ Failed to get status: {e}")
    
    async def cmd_search(self, query):
        """Web search."""
        if not query:
            print("❌ Please provide a search query")
            return
            
        try:
            result = await self.api_post("/web/search", {
                "query": query,
                "num_results": 5
            })
            
            print(f"\n🔍 Search Results: {query}")
            print("=" * 40)
            
            for i, item in enumerate(result.get('results', []), 1):
                print(f"{i}. {item.get('title', 'No title')}")
                print(f"   {item.get('url', '')}")
                print(f"   {item.get('snippet', '')[:100]}...")
                print()
                
        except Exception as e:
            print(f"❌ Search failed: {e}")
    
    async def cmd_ls(self, path="."):
        """List directory."""
        try:
            result = await self.api_post("/files/list", {"path": path})
            print(f"\n📁 Directory: {path}")
            print("=" * 40)
            
            items = result.get('items', [])
            if not items:
                print("(empty)")
                return
                
            for item in items:
                icon = "📁" if item.get('type') == 'directory' else "📄"
                print(f"{icon} {item.get('name', '')}")
                
        except Exception as e:
            print(f"❌ Failed to list directory: {e}")
    
    async def cmd_cat(self, filepath):
        """Show file content."""
        if not filepath:
            print("❌ Please specify a file")
            return
            
        try:
            result = await self.api_post("/files/read", {"path": filepath})
            print(f"\n📄 File: {filepath}")
            print("=" * 40)
            print(result.get('content', ''))
        except Exception as e:
            print(f"❌ Failed to read file: {e}")

    async def cmd_chat(self, args):
        """Enhanced chat with AI."""
        if not args:
            print("❌ Please provide a message")
            print("💡 Usage: chat <message> [--model <model>] [--stream] [--enhanced]")
            return

        # Parse arguments
        message_parts = []
        model = None
        stream = False
        enhanced = False

        i = 0
        while i < len(args):
            if args[i] == "--model" and i + 1 < len(args):
                model = args[i + 1]
                i += 2
            elif args[i] == "--stream":
                stream = True
                i += 1
            elif args[i] == "--enhanced":
                enhanced = True
                i += 1
            else:
                message_parts.append(args[i])
                i += 1

        if not message_parts:
            print("❌ Please provide a message")
            return

        message = " ".join(message_parts)

        try:
            # Choose endpoint based on enhanced flag
            endpoint = "/chat/enhanced" if enhanced else "/chat/completions"

            # Prepare request data
            request_data = {
                "messages": [{"role": "user", "content": message}],
                "stream": stream
            }

            if model:
                request_data["model"] = model

            if enhanced:
                request_data.update({
                    "context_type": "general",
                    "use_web_search": True,
                    "remember_conversation": True
                })

            print(f"\n🤖 AI Response ({endpoint.split('/')[-1]}):")
            print("=" * 60)

            result = await self.api_post(endpoint, request_data)
            choices = result.get('choices', [])
            if choices:
                content = choices[0].get('message', {}).get('content', 'No response')
                print(content)

                # Show additional info for enhanced chat
                if enhanced and 'usage' in result:
                    usage = result['usage']
                    print(f"\n📊 Usage: {usage.get('total_tokens', 0)} tokens")
            else:
                print("No response received")

        except Exception as e:
            print(f"❌ Chat failed: {e}")

    async def cmd_agent(self, args):
        """Execute AI agent tasks with intelligent auto-selection."""
        if not args:
            print("❌ Please provide a task description")
            print("💡 Usage: agent <task_description> [--type <task_type>] [--project <path>]")
            print("🤖 The AI will automatically select the best approach and tools!")
            print("📋 Optional task types: code, analyze, search, debug, review, optimize, security")
            return

        # Parse arguments (simplified)
        task_parts = []
        task_type = "auto"  # Let AI decide
        project_path = None

        i = 0
        while i < len(args):
            if args[i] == "--type" and i + 1 < len(args):
                task_type = args[i + 1]
                i += 2
            elif args[i] == "--project" and i + 1 < len(args):
                project_path = args[i + 1]
                i += 2
            else:
                task_parts.append(args[i])
                i += 1

        if not task_parts:
            print("❌ Please provide a task description")
            return

        description = " ".join(task_parts)

        try:
            print(f"\n🤖 Executing Intelligent Agent Task")
            print("=" * 60)
            print(f"📝 Task: {description}")
            if task_type != "auto":
                print(f"🎯 Type: {task_type}")
            if project_path:
                print(f"📁 Project: {project_path}")
            print("🧠 AI is analyzing and selecting optimal approach...")
            print("⏳ Processing...")

            # Prepare simplified agent task request
            request_data = {
                "task_type": task_type,
                "description": description,
                "auto_select_engines": True,
                "priority": "normal",
                "remember_result": True
            }

            if project_path:
                request_data["project_path"] = project_path

            result = await self.api_post("/agent/execute", request_data)

            print(f"\n✅ Task Completed Successfully!")
            print(f"🆔 Task ID: {result.get('task_id', 'Unknown')}")
            print(f"⏱️  Execution Time: {result.get('execution_time', 0):.2f}s")
            engines_used = result.get('engines_used', [])
            if engines_used:
                print(f"🔧 AI Selected Engines: {', '.join(engines_used)}")
            else:
                print("🔧 AI Used: Internal reasoning only")

            # Display result
            task_result = result.get('result', {})
            if 'error' in task_result:
                print(f"\n❌ Error: {task_result['error']}")
            else:
                print(f"\n📋 Result:")
                print("-" * 40)

                # Format result based on task type
                if task_type == "code":
                    if 'code' in task_result:
                        print(f"💻 Generated Code:\n{task_result['code']}")
                    if 'explanation' in task_result:
                        print(f"\n📖 Explanation:\n{task_result['explanation']}")
                elif task_type == "analyze":
                    if 'analysis' in task_result:
                        print(f"🔍 Analysis:\n{task_result['analysis']}")
                    if 'recommendations' in task_result:
                        print(f"\n💡 Recommendations:\n{task_result['recommendations']}")
                else:
                    # General result display
                    for key, value in task_result.items():
                        if key != 'error':
                            print(f"{key.title()}: {value}")

            # Show suggestions
            suggestions = result.get('suggestions', [])
            if suggestions:
                print(f"\n💡 Suggestions:")
                for i, suggestion in enumerate(suggestions, 1):
                    print(f"   {i}. {suggestion}")

        except Exception as e:
            print(f"❌ Agent task failed: {e}")

    async def process_command(self, line):
        """Process command."""
        if not line.strip():
            return
            
        parts = line.strip().split()
        cmd = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if cmd == "help":
            await self.cmd_help()
        elif cmd == "models":
            await self.cmd_models(args)
        elif cmd == "status":
            await self.cmd_status()
        elif cmd == "search":
            await self.cmd_search(" ".join(args))
        elif cmd == "ls":
            await self.cmd_ls(args[0] if args else ".")
        elif cmd == "cat":
            await self.cmd_cat(args[0] if args else "")
        elif cmd in ["chat", "ask"]:
            await self.cmd_chat(args)
        elif cmd == "agent":
            await self.cmd_agent(args)
        elif cmd in ["exit", "quit"]:
            self.running = False
        else:
            print(f"❌ Unknown command: {cmd}")
            print("Type 'help' for available commands")

    def setup_readline(self):
        """Setup readline for command completion."""
        if not READLINE_AVAILABLE or not self.completer:
            return

        try:
            # Set up completion
            readline.set_completer(self.completer.complete)
            readline.parse_and_bind('tab: complete')

            # Enable history
            readline.parse_and_bind('set editing-mode emacs')
            readline.parse_and_bind('set completion-ignore-case on')
            readline.parse_and_bind('set show-all-if-ambiguous on')

            # Set up history file
            history_file = os.path.expanduser('~/.reverie_cli_history')
            try:
                readline.read_history_file(history_file)
                # Limit history size
                readline.set_history_length(1000)
            except FileNotFoundError:
                pass  # History file doesn't exist yet

            # Save history on exit
            import atexit
            atexit.register(lambda: self._save_history(history_file))

        except Exception as e:
            print(f"⚠️  Failed to setup command completion: {e}")

    def _save_history(self, history_file: str):
        """Save command history to file."""
        try:
            if READLINE_AVAILABLE:
                readline.write_history_file(history_file)
        except Exception:
            pass  # Ignore errors when saving history

    async def run(self):
        """Run interactive client."""
        self.print_banner()

        # Setup command completion
        self.setup_readline()

        await self.start_session()

        try:
            # Try to connect to default server
            if not await self.check_connection():
                # If default connection fails, prompt for custom server
                if not self.prompt_custom_server():
                    return

                # Try connecting to custom server
                if not await self.check_connection():
                    print("❌ Failed to connect to custom server. Exiting.")
                    return

            print("\n" + "=" * 60)
            if READLINE_AVAILABLE:
                print("Type 'help' for commands, 'exit' to quit")
                print("💡 Use Tab for command completion, ↑↓ for history")
            else:
                print("Type 'help' for commands, 'exit' to quit")
            print("=" * 60)

            while self.running:
                try:
                    line = input("\nReverie CLI> ").strip()
                    if line:
                        await self.process_command(line)
                except KeyboardInterrupt:
                    print("\n👋 Goodbye!")
                    break
                except EOFError:
                    print("\n👋 Goodbye!")
                    break
                except Exception as e:
                    print(f"❌ Error: {e}")

        finally:
            await self.close_session()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Reverie CLI Client")
    parser.add_argument("--host", default="localhost", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    parser.add_argument("--command", help="Single command to execute")
    
    args = parser.parse_args()
    
    client = SimpleReverieClient(args.host, args.port)
    
    if args.command:
        await client.start_session()
        try:
            await client.process_command(args.command)
        finally:
            await client.close_session()
    else:
        await client.run()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
