@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Reverie CLI - Environment Setup Script (Fixed Version)
:: =============================================================================

title Reverie CLI - Environment Setup

echo.
echo ===============================================================================
echo                    Reverie CLI - Environment Setup
echo                   AI-Native Development Server Setup
echo ===============================================================================
echo.

:: Configuration
set "VENV_NAME=reverie_env"
set "PYTHON_MIN_VERSION=3.8"
set "PROJECT_DIR=%~dp0"

echo [INFO] Project Directory: %PROJECT_DIR%
echo.

:: Check Python installation
echo [INFO] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found. Please install Python %PYTHON_MIN_VERSION% or higher
    echo [INFO] Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Python version: %PYTHON_VERSION%

:: Check pip availability
echo [INFO] Checking pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip not available
    pause
    exit /b 1
)
echo [SUCCESS] pip available

:: Check virtual environment
echo.
echo [INFO] Checking virtual environment...
if exist "%VENV_NAME%" (
    echo [WARNING] Virtual environment already exists: %VENV_NAME%
    set /p "RECREATE=Recreate virtual environment? y/N: "
    if /i "!RECREATE!"=="y" (
        echo [INFO] Removing existing virtual environment...
        rmdir /s /q "%VENV_NAME%"
        goto CREATE_VENV
    ) else (
        echo [INFO] Using existing virtual environment
        goto ACTIVATE_VENV
    )
) else (
    goto CREATE_VENV
)

:CREATE_VENV
echo [INFO] Creating virtual environment: %VENV_NAME%...
python -m venv "%VENV_NAME%"
if errorlevel 1 (
    echo [ERROR] Failed to create virtual environment
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment created

:ACTIVATE_VENV
echo [INFO] Activating virtual environment...
call "%VENV_NAME%\Scripts\activate.bat"
if errorlevel 1 (
    echo [ERROR] Failed to activate virtual environment
    pause
    exit /b 1
)
echo [SUCCESS] Virtual environment activated

:: Upgrade pip
echo.
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo [WARNING] pip upgrade failed, continuing...
) else (
    echo [SUCCESS] pip upgraded
)

:: Install PyTorch with CUDA support
echo.
echo [INFO] Installing project dependencies...
echo [INFO] Installing PyTorch with CUDA 12.8 support...
python -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
if errorlevel 1 (
    echo [WARNING] PyTorch CUDA installation failed, trying CPU version...
    python -m pip install torch torchvision torchaudio
    if errorlevel 1 (
        echo [ERROR] PyTorch installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] PyTorch CPU version installed
) else (
    echo [SUCCESS] PyTorch CUDA 12.8 version installed
)

:: Install other dependencies
if exist "requirements.txt" (
    echo [INFO] Installing from requirements.txt...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Dependencies installation failed
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed
) else (
    echo [WARNING] requirements.txt not found
)

:: Install project in development mode
echo.
echo [INFO] Installing project in development mode...
if exist "pyproject.toml" (
    python -m pip install -e .
    if errorlevel 1 (
        echo [WARNING] Development mode installation failed
    ) else (
        echo [SUCCESS] Project installed in development mode
    )
)

:: Create necessary directories
echo.
echo [INFO] Creating necessary directories...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
echo [SUCCESS] Directories created

:: Create loader directory for model loading software
echo.
echo [INFO] Creating loader directory structure...
if not exist "loader" mkdir loader
if not exist "loader\llama.cpp" mkdir "loader\llama.cpp"
echo [SUCCESS] Loader directories created

:: Information about model loader setup
echo.
echo ===============================================================================
echo                    Model Loader Setup Information
echo ===============================================================================
echo.
echo [INFO] GGUF Model Support Setup:
echo   To use GGUF models, you need to download llama.cpp binaries.
echo   Run 'loader-Downloader.bat' to download and install model loaders.
echo.
echo [INFO] Supported Model Formats:
echo   - GGUF files: Single file models (use llama.cpp)
echo   - Directory models: HuggingFace Transformers format
echo.
echo [INFO] Model Storage:
echo   - Place GGUF files in: .\models\llm\gguf\
echo   - Place directory models in: .\models\llm\transformers\
echo   - Or place models directly in: .\models\llm\
echo.
echo [INFO] Next Steps:
echo   1. Run 'loader-Downloader.bat' to download llama.cpp (for GGUF support)
echo   2. Place your model files in the appropriate directories
echo   3. Start Reverie CLI and use 'models scan' to detect models
echo.

:: Verify installation
echo.
echo [PROGRESS] Verifying Reverie CLI installation...
echo [PROGRESS] Testing module import...
python -c "import reverie_cli; print('SUCCESS: Reverie CLI module imported successfully')" 2>nul
if errorlevel 1 (
    echo [WARNING] Module import test failed, but environment setup completed
) else (
    echo [SUCCESS] Installation verified
)

:: Display completion information
echo.
echo [PROGRESS] Finalizing setup...
echo ===============================================================================
echo                           Setup Complete!
echo ===============================================================================
echo.
echo [SUCCESS] Virtual environment: %VENV_NAME%
echo [SUCCESS] Python version: %PYTHON_VERSION%
echo [SUCCESS] Project directory: %PROJECT_DIR%
echo.
echo [INFO] Next steps:
echo   1. Run 'start.bat' to start the application
echo   2. Or activate environment manually: %VENV_NAME%\Scripts\activate.bat
echo.

:: Ask if user wants to start immediately
set /p "START_NOW=Start Reverie CLI now? y/N: "
if /i "!START_NOW!"=="y" (
    echo.
    echo [PROGRESS] Starting Reverie CLI...
    call start.bat
) else (
    echo.
    echo [PROGRESS] Setup completed. Run 'start.bat' when ready.
)

echo.
pause
