@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Reverie CLI Client - Build Script
:: Compiles the Python client to a standalone executable
:: =============================================================================

title Reverie CLI Client - Build Script

echo.
echo ===============================================================================
echo                    Reverie CLI Client - Build Script
echo                   Compile Python Client to Executable
echo ===============================================================================
echo.

:: Configuration
set "SCRIPT_DIR=%~dp0"
set "CLIENT_SCRIPT=%SCRIPT_DIR%simple_client.py"
set "BACKUP_SCRIPT=%SCRIPT_DIR%reverie_client.py"
set "OUTPUT_NAME=ReverieCli.exe"
set "DIST_DIR=%SCRIPT_DIR%dist"
set "BUILD_DIR=%SCRIPT_DIR%build"

echo [INFO] Script Directory: %SCRIPT_DIR%
echo [INFO] Client Script: %CLIENT_SCRIPT%
echo [INFO] Output Name: %OUTPUT_NAME%
echo.

:: Check if Python is available
echo [PROGRESS] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo [INFO] Please install Python and ensure it's in your PATH
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Found Python: %PYTHON_VERSION%

:: Check if pip is available
echo [PROGRESS] Checking pip installation...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip is not available
    echo [INFO] Please ensure pip is installed with Python
    pause
    exit /b 1
)
echo [SUCCESS] pip is available

:: Check if client script exists
if not exist "%CLIENT_SCRIPT%" (
    echo [WARNING] Simple client script not found: %CLIENT_SCRIPT%
    if exist "%BACKUP_SCRIPT%" (
        echo [INFO] Using backup script: %BACKUP_SCRIPT%
        set "CLIENT_SCRIPT=%BACKUP_SCRIPT%"
    ) else (
        echo [ERROR] No client script found
        echo [INFO] Please ensure simple_client.py or reverie_client.py exists
        pause
        exit /b 1
    )
)
echo [SUCCESS] Client script found: %CLIENT_SCRIPT%

:: Install required dependencies
echo.
echo [PROGRESS] Installing required dependencies...
echo [INFO] Installing aiohttp for HTTP client functionality...
python -m pip install aiohttp
if errorlevel 1 (
    echo [ERROR] Failed to install aiohttp
    pause
    exit /b 1
)
echo [SUCCESS] aiohttp installed

:: Install PyInstaller
echo [PROGRESS] Installing PyInstaller...
python -m pip install pyinstaller
if errorlevel 1 (
    echo [ERROR] Failed to install PyInstaller
    echo [INFO] PyInstaller is required to build the executable
    pause
    exit /b 1
)
echo [SUCCESS] PyInstaller installed

:: Clean previous builds
echo.
echo [PROGRESS] Cleaning previous builds...
if exist "%DIST_DIR%" (
    rmdir /s /q "%DIST_DIR%" >nul 2>&1
    echo [SUCCESS] Cleaned dist directory
)
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%" >nul 2>&1
    echo [SUCCESS] Cleaned build directory
)
if exist "*.spec" (
    del "*.spec" >nul 2>&1
    echo [SUCCESS] Cleaned spec files
)

:: Build the executable
echo.
echo [PROGRESS] Building executable with PyInstaller...
echo [INFO] This may take a few minutes...
echo.

:: Try different PyInstaller invocation methods
echo [INFO] Checking PyInstaller installation...
python -c "import PyInstaller; print('PyInstaller version:', PyInstaller.__version__)" 2>nul
if errorlevel 1 (
    echo [WARNING] PyInstaller module not accessible via python -m
    echo [INFO] Trying direct pyinstaller command...

    :: Try direct pyinstaller command
    pyinstaller --version >nul 2>&1
    if not errorlevel 1 (
        echo [SUCCESS] Using direct pyinstaller command
        pyinstaller --onefile --name "%OUTPUT_NAME:~0,-4%" --distpath "%DIST_DIR%" --workpath "%BUILD_DIR%" --console --hidden-import=aiohttp --hidden-import=asyncio --hidden-import=json --hidden-import=argparse "%CLIENT_SCRIPT%"
    ) else (
        echo [ERROR] PyInstaller not accessible via direct command either
        echo [INFO] Trying to fix PyInstaller installation...

        :: Reinstall PyInstaller
        python -m pip uninstall pyinstaller -y >nul 2>&1
        python -m pip install pyinstaller --force-reinstall
        if errorlevel 1 (
            echo [ERROR] Failed to reinstall PyInstaller
            pause
            exit /b 1
        )

        :: Try again with python -m
        python -m PyInstaller --onefile --name "%OUTPUT_NAME:~0,-4%" --distpath "%DIST_DIR%" --workpath "%BUILD_DIR%" --console --hidden-import=aiohttp --hidden-import=asyncio --hidden-import=json --hidden-import=argparse "%CLIENT_SCRIPT%"
    )
) else (
    echo [SUCCESS] Using python -m PyInstaller
    python -m PyInstaller ^
        --onefile ^
        --name "%OUTPUT_NAME:~0,-4%" ^
        --distpath "%DIST_DIR%" ^
        --workpath "%BUILD_DIR%" ^
        --specpath "%SCRIPT_DIR%" ^
        --console ^
        --hidden-import=aiohttp ^
        --hidden-import=asyncio ^
        --hidden-import=json ^
        --hidden-import=argparse ^
        --hidden-import=urllib.parse ^
        --hidden-import=pathlib ^
        "%CLIENT_SCRIPT%"
)

if errorlevel 1 (
    echo.
    echo [ERROR] Build failed
    echo [INFO] Check the output above for error details
    pause
    exit /b 1
)

:: Check if executable was created
set "EXECUTABLE_PATH=%DIST_DIR%\%OUTPUT_NAME:~0,-4%.exe"
if not exist "%EXECUTABLE_PATH%" (
    echo.
    echo [ERROR] Executable not found at expected location: %EXECUTABLE_PATH%
    echo [INFO] Build may have failed silently
    pause
    exit /b 1
)

:: Rename to desired name if different
if not "%OUTPUT_NAME:~0,-4%.exe"=="%OUTPUT_NAME%" (
    ren "%EXECUTABLE_PATH%" "%OUTPUT_NAME%" >nul 2>&1
    set "EXECUTABLE_PATH=%DIST_DIR%\%OUTPUT_NAME%"
)

:: Verify executable
echo.
echo [PROGRESS] Verifying executable...
"%EXECUTABLE_PATH%" --help >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Executable verification failed, but file exists
    echo [INFO] The executable may still work for normal operations
) else (
    echo [SUCCESS] Executable verified successfully
)

:: Get file size
for %%A in ("%EXECUTABLE_PATH%") do set FILE_SIZE=%%~zA
set /a FILE_SIZE_MB=%FILE_SIZE% / 1024 / 1024

:: Clean up build artifacts (optional)
echo [PROGRESS] Cleaning up build artifacts...
if exist "%BUILD_DIR%" (
    rmdir /s /q "%BUILD_DIR%" >nul 2>&1
)
if exist "*.spec" (
    del "*.spec" >nul 2>&1
)
echo [SUCCESS] Build artifacts cleaned

:: Success message
echo.
echo ===============================================================================
echo                           Build Completed Successfully!
echo ===============================================================================
echo.
echo [SUCCESS] Executable created: %EXECUTABLE_PATH%
echo [SUCCESS] File size: %FILE_SIZE_MB% MB
echo.
echo [INFO] Usage Examples:
echo   %OUTPUT_NAME% --host ************* --port 8000
echo   %OUTPUT_NAME% --command "models"
echo   %OUTPUT_NAME%  (for interactive mode)
echo.
echo [INFO] The executable is standalone and can be distributed without Python
echo [INFO] Place it in any directory and run it to connect to Reverie CLI Server
echo.

:: Test run option
set /p "test_run=Would you like to test the executable now? (y/n): "
if /i "%test_run%"=="y" (
    echo.
    echo [INFO] Starting test run...
    echo [INFO] This will try to connect to localhost:8000
    echo [INFO] Press Ctrl+C to exit the test
    echo.
    pause
    "%EXECUTABLE_PATH%" --command "help"
)

echo.
echo [INFO] Build script completed. Press any key to exit.
pause >nul
exit /b 0
