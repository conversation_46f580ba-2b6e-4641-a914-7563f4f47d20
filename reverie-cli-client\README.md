# Reverie CLI Client

A standalone command-line client for interacting with Reverie CLI Server Mode.

## Features

- **File Operations**: List directories, read files, create/delete files
- **Web Search**: Search the web and fetch content from URLs
- **AI Interaction**: Chat with AI models, ask questions
- **Model Management**: List and load AI models
- **System Status**: Check server health and status
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Quick Start

### 1. Build the Executable

Run the build script to compile the client:

```bash
build.bat
```

This will:
- Install required dependencies (aiohttp, pyinstaller)
- Compile the Python script to a standalone executable
- Create `ReverieCli.exe` in the `dist/` directory

### 2. Start Reverie CLI Server

Make sure the Reverie CLI server is running:

```bash
# In the main Reverie CLI directory
start.bat
```

### 3. Run the Client

```bash
# Interactive mode (default: localhost:8000)
ReverieCli.exe

# Connect to specific server
ReverieCli.exe --host ************* --port 8000

# Execute single command
ReverieCli.exe --command "models"
```

## Available Commands

### File Operations
```bash
ls [path]              # List directory contents
cat <file>             # Display file contents  
mkdir <dir>            # Create directory
rm <file>              # Delete file
touch <file>           # Create empty file
pwd                    # Show current directory
```

### Web & Search
```bash
search <query>         # Web search
fetch <url>            # Fetch web page content
```

### AI & Models
```bash
models                 # List available models
load <model>           # Load a model
chat <message>         # Chat with AI
ask <question>         # Ask AI a question
```

### System
```bash
status                 # Show server status
tools                  # List available tools
help                   # Show help
exit                   # Exit client
```

## Usage Examples

### Basic File Operations
```bash
Reverie CLI> ls
📁 Directory: .
==================================================
📁 src
📁 tests
📄 main.py (1234 bytes)
📄 README.md (5678 bytes)

Reverie CLI> cat main.py
📄 File: main.py
==================================================
#!/usr/bin/env python3
print("Hello, World!")
```

### Web Search
```bash
Reverie CLI> search Python async programming
🔍 Search Results for: Python async programming
==================================================
1. Python Asyncio Tutorial
   URL: https://docs.python.org/3/library/asyncio.html
   Official Python documentation for asyncio...

2. Real Python - Async IO in Python
   URL: https://realpython.com/async-io-python/
   A complete guide to asynchronous programming...
```

### AI Interaction
```bash
Reverie CLI> models
🤖 Available Models
==================================================
🟢 gpt-3.5-turbo (openai)
⚪ llama-7b (gguf)
⚪ mistral-7b (transformers)

Reverie CLI> chat How do I optimize this Python code?
🤖 AI Response:
==================================================
To optimize Python code, consider these approaches:
1. Use built-in functions and libraries
2. Avoid unnecessary loops
3. Use list comprehensions
4. Profile your code to find bottlenecks...
```

## Configuration

### Command Line Options

```bash
ReverieCli.exe [OPTIONS]

Options:
  --host TEXT     Server hostname or IP (default: localhost)
  --port INTEGER  Server port number (default: 8000)
  --command TEXT  Single command to execute
  --help         Show help message
```

### Environment Variables

You can also set default connection parameters:

```bash
set REVERIE_HOST=*************
set REVERIE_PORT=8000
ReverieCli.exe
```

## API Endpoints Used

The client interacts with these Reverie CLI Server endpoints:

- `/api/v1/health` - Server health check
- `/api/v1/models` - Model management
- `/api/v1/chat/completions` - AI chat
- `/api/v1/web/search` - Web search
- `/api/v1/web/fetch` - Web content fetch
- `/api/v1/files/list` - File listing
- `/api/v1/files/read` - File reading
- `/api/v1/tools` - Tool management

## Building from Source

### Requirements

- Python 3.8+
- pip (Python package manager)

### Dependencies

The build script automatically installs:
- `aiohttp` - Async HTTP client
- `pyinstaller` - Python to executable compiler

### Manual Build

If you prefer to build manually:

```bash
# Install dependencies
pip install aiohttp pyinstaller

# Build executable
pyinstaller --onefile --name ReverieCli reverie_client.py
```

## Troubleshooting

### Connection Issues

1. **Server not running**: Make sure Reverie CLI server is started
2. **Wrong host/port**: Check the server address and port
3. **Firewall**: Ensure the port is not blocked by firewall

### Build Issues

1. **Python not found**: Install Python and add to PATH
2. **pip not available**: Reinstall Python with pip
3. **PyInstaller fails**: Try updating PyInstaller: `pip install --upgrade pyinstaller`

### Runtime Issues

1. **Command not found**: Type `help` to see available commands
2. **API errors**: Check server logs for detailed error information
3. **File operations fail**: Ensure proper permissions and paths

## Development

### Project Structure

```
reverie-cli-client/
├── reverie_client.py    # Main client code
├── build.bat           # Build script
├── README.md           # This file
└── dist/              # Built executables (created by build)
    └── ReverieCli.exe
```

### Adding New Commands

To add new commands, modify the `commands` dictionary in `reverie_client.py`:

```python
commands = {
    "help": self.cmd_help,
    "ls": self.cmd_ls,
    "your_command": self.cmd_your_command,  # Add here
    # ...
}

async def cmd_your_command(self, args: List[str]):
    """Your command implementation."""
    # Implementation here
```

## License

This client is part of the Reverie CLI project and follows the same license terms.

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review server logs for API errors
3. Ensure server and client versions are compatible
