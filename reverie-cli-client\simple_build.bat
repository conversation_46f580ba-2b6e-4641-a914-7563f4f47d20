@echo off
echo Building Reverie CLI Client...

:: Check if simple_client.py exists
if not exist "simple_client.py" (
    echo Error: simple_client.py not found
    pause
    exit /b 1
)

:: Install dependencies
echo Installing dependencies...
python -m pip install aiohttp pyinstaller

:: Remove pathlib if it exists (conflicts with PyInstaller)
python -m pip uninstall pathlib -y >nul 2>&1

:: Clean previous builds
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

:: Build executable
echo Building executable...
python -m PyInstaller --onefile --console --name ReverieCli simple_client.py

:: Check result
if exist "dist\ReverieCli.exe" (
    echo.
    echo ===============================================
    echo Build successful!
    echo Executable: dist\ReverieCli.exe
    echo ===============================================
    echo.
    
    :: Test the executable
    set /p "test=Test the executable? (y/n): "
    if /i "%test%"=="y" (
        echo Testing executable...
        "dist\ReverieCli.exe" --help
    )
) else (
    echo.
    echo Build failed!
    echo Check the output above for errors.
)

pause
