"""
Web search and content retrieval endpoints.
"""

from typing import List, Optional, Dict, Any
import asyncio

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger
from reverie_cli.api.dependencies import get_tool_manager_dep


router = APIRouter()
logger = get_logger("web")


class WebSearchRequest(BaseModel):
    """Web search request."""
    query: str = Field(..., description="Search query")
    num_results: int = Field(5, description="Number of results to return", ge=1, le=10)
    include_content: bool = Field(False, description="Whether to fetch full content")


class WebSearchResult(BaseModel):
    """Web search result."""
    title: str
    url: str
    snippet: str
    content: Optional[str] = None


class WebSearchResponse(BaseModel):
    """Web search response."""
    query: str
    results: List[WebSearchResult]
    total_results: int
    search_time: float


class WebFetchRequest(BaseModel):
    """Web content fetch request."""
    url: str = Field(..., description="URL to fetch content from")


class WebFetchResponse(BaseModel):
    """Web content fetch response."""
    url: str
    title: str
    content: str
    success: bool
    message: str


@router.post("/web/search", response_model=WebSearchResponse)
async def web_search(request: WebSearchRequest, tool_manager=Depends(get_tool_manager_dep)):
    """Perform web search."""
    try:
        logger.info(f"Web search: {request.query}")
        
        # Execute web search tool
        search_result = await tool_manager.execute_tool(
            "web_search",
            {
                "query": request.query,
                "num_results": request.num_results
            }
        )
        
        if not search_result.success:
            raise HTTPException(status_code=500, detail=search_result.message)
        
        # Parse results
        results = []
        search_data = search_result.result
        
        if isinstance(search_data, dict) and "results" in search_data:
            for item in search_data["results"]:
                result = WebSearchResult(
                    title=item.get("title", ""),
                    url=item.get("url", ""),
                    snippet=item.get("snippet", ""),
                )
                
                # Fetch full content if requested
                if request.include_content:
                    try:
                        fetch_result = await tool_manager.execute_tool(
                            "web_fetch",
                            {"url": item.get("url", "")}
                        )
                        if fetch_result.success:
                            result.content = fetch_result.result.get("content", "")
                    except Exception as e:
                        logger.warning(f"Failed to fetch content for {item.get('url', '')}: {e}")
                
                results.append(result)
        
        return WebSearchResponse(
            query=request.query,
            results=results,
            total_results=len(results),
            search_time=search_result.execution_time
        )
        
    except Exception as e:
        logger.error(f"Web search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Web search failed: {e}")


@router.post("/web/fetch", response_model=WebFetchResponse)
async def web_fetch(request: WebFetchRequest, tool_manager=Depends(get_tool_manager_dep)):
    """Fetch content from a web page."""
    try:
        logger.info(f"Web fetch: {request.url}")
        
        # Execute web fetch tool
        fetch_result = await tool_manager.execute_tool(
            "web_fetch",
            {"url": request.url}
        )
        
        if fetch_result.success:
            content_data = fetch_result.result
            return WebFetchResponse(
                url=request.url,
                title=content_data.get("title", ""),
                content=content_data.get("content", ""),
                success=True,
                message="Content fetched successfully"
            )
        else:
            return WebFetchResponse(
                url=request.url,
                title="",
                content="",
                success=False,
                message=fetch_result.message
            )
            
    except Exception as e:
        logger.error(f"Web fetch failed: {e}")
        raise HTTPException(status_code=500, detail=f"Web fetch failed: {e}")


@router.get("/web/tools")
async def list_web_tools():
    """List available web-related tools."""
    return {
        "tools": [
            {
                "name": "web_search",
                "description": "Search the web for information",
                "parameters": ["query", "num_results"]
            },
            {
                "name": "web_fetch", 
                "description": "Fetch content from a web page",
                "parameters": ["url"]
            }
        ]
    }


@router.get("/web/status")
async def web_engine_status(tool_manager=Depends(get_tool_manager_dep)):
    """Get web engine status."""
    try:
        # Check if web tools are available
        tools_info = tool_manager.list_available_tools()
        web_tools = [tool for tool in tools_info if "web" in tool.name.lower()]
        
        return {
            "status": "ready" if web_tools else "unavailable",
            "available_tools": [tool.name for tool in web_tools],
            "total_web_tools": len(web_tools)
        }
        
    except Exception as e:
        logger.error(f"Failed to get web engine status: {e}")
        return {
            "status": "error",
            "message": str(e),
            "available_tools": [],
            "total_web_tools": 0
        }
