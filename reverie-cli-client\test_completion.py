#!/usr/bin/env python3
"""
Test script for command completion functionality.
"""

import sys
import os
from pathlib import Path

# Add the client directory to path
sys.path.insert(0, str(Path(__file__).parent))

try:
    import readline
    READLINE_AVAILABLE = True
except ImportError:
    READLINE_AVAILABLE = False

from simple_client import CommandCompleter


def test_command_completion():
    """Test the command completion functionality."""
    print("🧪 Testing Command Completion Functionality")
    print("=" * 60)
    
    if not READLINE_AVAILABLE:
        print("⚠️  readline not available - skipping completion tests")
        return False
    
    completer = CommandCompleter()
    
    # Test cases for command completion
    test_cases = [
        # Basic command completion
        {
            "input": "he",
            "expected": ["help"],
            "description": "Complete 'he' to 'help'"
        },
        {
            "input": "ch",
            "expected": ["chat"],
            "description": "Complete 'ch' to 'chat'"
        },
        {
            "input": "ag",
            "expected": ["agent"],
            "description": "Complete 'ag' to 'agent'"
        },
        {
            "input": "mod",
            "expected": ["models"],
            "description": "Complete 'mod' to 'models'"
        },
        
        # Multiple matches
        {
            "input": "e",
            "expected": ["exit"],
            "description": "Complete 'e' (should include 'exit')"
        },
        
        # No matches
        {
            "input": "xyz",
            "expected": [],
            "description": "No completion for 'xyz'"
        }
    ]
    
    print("\n1. Testing Basic Command Completion")
    print("-" * 40)
    
    success_count = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        input_text = test_case["input"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        print(f"\n{i}. {description}")
        print(f"   Input: '{input_text}'")
        
        # Get all completions
        matches = []
        state = 0
        while True:
            match = completer.complete(input_text, state)
            if match is None:
                break
            matches.append(match)
            state += 1
        
        print(f"   Found: {matches}")
        print(f"   Expected: {expected}")
        
        # Check if expected completions are found
        if expected:
            if all(exp in matches for exp in expected):
                print("   ✅ PASS")
                success_count += 1
            else:
                print("   ❌ FAIL")
        else:
            if not matches:
                print("   ✅ PASS")
                success_count += 1
            else:
                print("   ❌ FAIL")
    
    print(f"\n📊 Basic Completion Results: {success_count}/{total_tests} tests passed")
    
    # Test subcommand completion
    print("\n2. Testing Subcommand Completion")
    print("-" * 40)
    
    # Mock readline buffer for subcommand testing
    class MockReadline:
        def __init__(self, buffer):
            self.buffer = buffer
        
        def get_line_buffer(self):
            return self.buffer
    
    # Temporarily replace readline for testing
    original_readline = None
    if 'readline' in sys.modules:
        original_readline = sys.modules['readline']
    
    subcommand_tests = [
        {
            "buffer": "models ",
            "input": "l",
            "expected": ["load"],
            "description": "Complete 'models l' to 'load'"
        },
        {
            "buffer": "models ",
            "input": "c",
            "expected": ["current"],
            "description": "Complete 'models c' to 'current'"
        },
        {
            "buffer": "chat ",
            "input": "--m",
            "expected": ["--model"],
            "description": "Complete 'chat --m' to '--model'"
        },
        {
            "buffer": "agent ",
            "input": "--t",
            "expected": ["--type"],
            "description": "Complete 'agent --t' to '--type'"
        }
    ]
    
    subcommand_success = 0
    subcommand_total = len(subcommand_tests)
    
    for i, test_case in enumerate(subcommand_tests, 1):
        buffer = test_case["buffer"]
        input_text = test_case["input"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        print(f"\n{i}. {description}")
        print(f"   Buffer: '{buffer}'")
        print(f"   Input: '{input_text}'")
        
        # Mock readline
        sys.modules['readline'] = MockReadline(buffer)
        
        # Get completions
        matches = []
        state = 0
        while True:
            match = completer.complete(input_text, state)
            if match is None:
                break
            matches.append(match)
            state += 1
        
        print(f"   Found: {matches}")
        print(f"   Expected: {expected}")
        
        if expected:
            if all(exp in matches for exp in expected):
                print("   ✅ PASS")
                subcommand_success += 1
            else:
                print("   ❌ FAIL")
        else:
            if not matches:
                print("   ✅ PASS")
                subcommand_success += 1
            else:
                print("   ❌ FAIL")
    
    # Restore original readline
    if original_readline:
        sys.modules['readline'] = original_readline
    
    print(f"\n📊 Subcommand Completion Results: {subcommand_success}/{subcommand_total} tests passed")
    
    # Overall results
    total_success = success_count + subcommand_success
    total_all = total_tests + subcommand_total
    
    print(f"\n🎯 Overall Results: {total_success}/{total_all} tests passed")
    
    if total_success == total_all:
        print("✅ All completion tests passed!")
        return True
    else:
        print("⚠️  Some completion tests failed")
        return False


def test_history_functionality():
    """Test command history functionality."""
    print("\n3. Testing Command History")
    print("-" * 40)
    
    if not READLINE_AVAILABLE:
        print("⚠️  readline not available - skipping history tests")
        return True
    
    print("✅ History file support: ~/.reverie_cli_history")
    print("✅ History length limit: 1000 commands")
    print("✅ Auto-save on exit: Enabled")
    print("✅ History navigation: ↑↓ arrow keys")
    
    return True


def test_completion_features():
    """Test additional completion features."""
    print("\n4. Testing Completion Features")
    print("-" * 40)
    
    if not READLINE_AVAILABLE:
        print("⚠️  readline not available - skipping feature tests")
        return True
    
    print("✅ Tab completion: Enabled")
    print("✅ Case-insensitive completion: Enabled")
    print("✅ Show all ambiguous matches: Enabled")
    print("✅ Emacs editing mode: Enabled")
    
    return True


if __name__ == "__main__":
    try:
        print("🚀 Starting Command Completion Tests")
        print("=" * 60)
        
        if not READLINE_AVAILABLE:
            print("⚠️  WARNING: readline module not available")
            print("   Command completion will be disabled")
            print("   Install readline support for full functionality")
        
        test1 = test_command_completion()
        test2 = test_history_functionality()
        test3 = test_completion_features()
        
        print("\n" + "=" * 60)
        if test1 and test2 and test3:
            print("🎉 ALL COMPLETION TESTS PASSED!")
            print("Command completion is ready for use!")
        else:
            print("⚠️  Some tests failed, but basic functionality should work")
        
        print("\n💡 Usage Tips:")
        print("   • Press Tab to complete commands")
        print("   • Use ↑↓ arrows to navigate history")
        print("   • Type partial commands and press Tab")
        print("   • Works with command options like --model, --type")
        
    except KeyboardInterrupt:
        print("\n\n👋 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        sys.exit(1)
