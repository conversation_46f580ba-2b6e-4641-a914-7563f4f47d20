"""
Llama.cpp manager for downloading, installing and managing llama.cpp binaries.
"""

import asyncio
import os
import zipfile
import shutil
from pathlib import Path
from typing import Optional, Dict, List, Tuple
from urllib.parse import urlparse
import tempfile
from datetime import datetime

import httpx
from rich.console import Console
from rich.progress import Progress, DownloadColumn, BarColumn, TextColumn, TimeRemainingColumn

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import ModelError


logger = get_logger("llama_cpp_manager")
console = Console()


class LlamaCppManager:
    """Manager for llama.cpp binaries and tools."""
    
    # Available llama.cpp versions and download URLs
    AVAILABLE_VERSIONS = {
        "b6106-cuda-124": {
            "url": "https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cuda-12.4-x64.zip",
            "description": "Build 6106 - Windows CUDA 12.4 x64",
            "platform": "win-cuda-124-x64"
        },
        "b6106-cpu": {
            "url": "https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cpu-x64.zip",
            "description": "Build 6106 - Windows CPU x64",
            "platform": "win-cpu-x64"
        },
        "b6106-cuda-118": {
            "url": "https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cuda-11.8-x64.zip",
            "description": "Build 6106 - Windows CUDA 11.8 x64",
            "platform": "win-cuda-118-x64"
        },
        "latest": {
            "url": "https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cuda-12.4-x64.zip",
            "description": "Latest stable build (b6106) - Windows CUDA 12.4 x64",
            "platform": "win-cuda-124-x64"
        }
    }
    
    def __init__(self, loader_dir: Optional[Path] = None):
        """Initialize the llama.cpp manager.

        Args:
            loader_dir: Path to loader directory. Defaults to ./loader/llama.cpp
        """
        if loader_dir is None:
            loader_dir = Path.cwd() / "loader" / "llama.cpp"

        self.loader_dir = Path(loader_dir)

        # Ensure directories exist
        self.loader_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"LlamaCppManager initialized with directory: {self.loader_dir}")
    
    def is_installed(self) -> bool:
        """Check if llama.cpp is installed."""
        cli_exe = self.loader_dir / "llama-cli.exe"
        server_exe = self.loader_dir / "llama-server.exe"
        return cli_exe.exists() and server_exe.exists()
    
    def get_installed_version(self) -> Optional[str]:
        """Get the installed version of llama.cpp."""
        version_file = self.loader_dir / "version.txt"
        if version_file.exists():
            try:
                return version_file.read_text().strip()
            except Exception as e:
                logger.warning(f"Failed to read version file: {e}")
        return None
    
    def list_available_versions(self) -> Dict[str, Dict[str, str]]:
        """List all available llama.cpp versions."""
        return self.AVAILABLE_VERSIONS.copy()
    
    async def download_and_install(self, version: str = "latest", force: bool = False) -> bool:
        """Download and install llama.cpp.
        
        Args:
            version: Version to download (default: "latest")
            force: Force reinstall even if already installed
            
        Returns:
            True if successful, False otherwise
        """
        if not force and self.is_installed():
            installed_version = self.get_installed_version()
            logger.info(f"llama.cpp is already installed (version: {installed_version})")
            console.print(f"[green]✅ llama.cpp is already installed (version: {installed_version})[/green]")
            return True
        
        if version not in self.AVAILABLE_VERSIONS:
            logger.error(f"Unknown version: {version}")
            console.print(f"[red]❌ Unknown version: {version}[/red]")
            console.print(f"[dim]Available versions: {', '.join(self.AVAILABLE_VERSIONS.keys())}[/dim]")
            return False
        
        version_info = self.AVAILABLE_VERSIONS[version]
        download_url = version_info["url"]
        
        logger.info(f"Downloading llama.cpp version {version} from {download_url}")
        console.print(f"[blue]📥 Downloading llama.cpp version {version}...[/blue]")
        
        try:
            # Download the file
            zip_path = await self._download_file(download_url)
            
            # Extract the archive
            console.print("[blue]📦 Extracting archive...[/blue]")
            success = await self._extract_archive(zip_path, version)
            
            # Cleanup temporary file
            if zip_path.exists():
                zip_path.unlink()
            
            if success:
                # Save version info
                version_file = self.loader_dir / "version.txt"
                version_info = self.AVAILABLE_VERSIONS[version]
                version_file.write_text(f"{version_info['description']}\nDownloaded on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\nSource: {version_info['url']}")

                logger.info(f"llama.cpp version {version} installed successfully")
                console.print(f"[green]✅ llama.cpp version {version} installed successfully[/green]")
                return True
            else:
                logger.error(f"Failed to extract llama.cpp version {version}")
                console.print(f"[red]❌ Failed to extract llama.cpp version {version}[/red]")
                return False
                
        except Exception as e:
            logger.error(f"Failed to download/install llama.cpp: {e}")
            console.print(f"[red]❌ Failed to download/install llama.cpp: {e}[/red]")
            return False
    
    async def _download_file(self, url: str) -> Path:
        """Download a file with progress bar.
        
        Args:
            url: URL to download
            
        Returns:
            Path to downloaded file
        """
        # Create temporary file
        temp_dir = Path(tempfile.gettempdir())
        filename = Path(urlparse(url).path).name
        temp_path = temp_dir / f"reverie_llama_cpp_{filename}"
        
        async with httpx.AsyncClient(follow_redirects=True) as client:
            # Get file size
            response = await client.head(url)
            total_size = int(response.headers.get("content-length", 0))
            
            # Download with progress bar
            with Progress(
                TextColumn("[bold blue]{task.description}"),
                BarColumn(),
                DownloadColumn(),
                TimeRemainingColumn(),
                console=console,
            ) as progress:
                task = progress.add_task(f"Downloading {filename}", total=total_size)
                
                with open(temp_path, "wb") as f:
                    async with client.stream("GET", url) as response:
                        response.raise_for_status()
                        async for chunk in response.aiter_bytes(chunk_size=8192):
                            f.write(chunk)
                            progress.update(task, advance=len(chunk))
        
        return temp_path
    
    async def _extract_archive(self, zip_path: Path, version: str) -> bool:
        """Extract the downloaded archive.

        Args:
            zip_path: Path to zip file
            version: Version being installed

        Returns:
            True if successful, False otherwise
        """
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Extract to temporary directory first
                temp_extract_dir = self.loader_dir / f"temp_extract_{version}"
                temp_extract_dir.mkdir(exist_ok=True)

                try:
                    zip_ref.extractall(temp_extract_dir)

                    # Find the executable files (llama-cli.exe and llama-server.exe)
                    cli_exe_found = None
                    server_exe_found = None

                    for root, dirs, files in os.walk(temp_extract_dir):
                        for file in files:
                            if file == "llama-cli.exe":
                                cli_exe_found = Path(root) / file
                            elif file == "llama-server.exe":
                                server_exe_found = Path(root) / file

                    if cli_exe_found is None:
                        logger.error("Could not find llama-cli.exe in extracted files")
                        return False

                    # Copy executable files to loader directory
                    shutil.copy2(cli_exe_found, self.loader_dir / "llama-cli.exe")
                    logger.info(f"Copied llama-cli.exe to {self.loader_dir}")

                    if server_exe_found:
                        shutil.copy2(server_exe_found, self.loader_dir / "llama-server.exe")
                        logger.info(f"Copied llama-server.exe to {self.loader_dir}")
                    else:
                        logger.warning("llama-server.exe not found in archive")

                    # Copy any DLL files that might be needed
                    for root, dirs, files in os.walk(temp_extract_dir):
                        for file in files:
                            if file.endswith('.dll'):
                                dll_path = Path(root) / file
                                shutil.copy2(dll_path, self.loader_dir / file)
                                logger.debug(f"Copied {file} to {self.loader_dir}")

                    # Copy any additional documentation files
                    for root, dirs, files in os.walk(temp_extract_dir):
                        for file in files:
                            if file.lower().endswith(('.txt', '.md', '.json', '.license')):
                                doc_path = Path(root) / file
                                try:
                                    shutil.copy2(doc_path, self.loader_dir / file)
                                    logger.debug(f"Copied documentation {file} to {self.loader_dir}")
                                except Exception as e:
                                    logger.debug(f"Failed to copy {file}: {e}")

                    return True

                finally:
                    # Cleanup temporary extraction directory
                    if temp_extract_dir.exists():
                        shutil.rmtree(temp_extract_dir)

        except Exception as e:
            logger.error(f"Failed to extract archive: {e}")
            return False
    
    def get_executable_path(self, executable: str = "cli") -> Optional[Path]:
        """Get path to llama.cpp executable.

        Args:
            executable: Executable name ("cli", "server", etc.)

        Returns:
            Path to executable or None if not found
        """
        if executable == "cli" or executable == "main":
            exe_path = self.loader_dir / "llama-cli.exe"
        elif executable == "server":
            exe_path = self.loader_dir / "llama-server.exe"
        else:
            exe_path = self.loader_dir / f"llama-{executable}.exe"

        return exe_path if exe_path.exists() else None
    
    def uninstall(self) -> bool:
        """Uninstall llama.cpp.

        Returns:
            True if successful, False otherwise
        """
        try:
            if self.loader_dir.exists():
                # Remove all files in the loader directory
                for file_path in self.loader_dir.iterdir():
                    if file_path.is_file():
                        file_path.unlink()
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)

                logger.info("llama.cpp uninstalled successfully")
                console.print("[green]✅ llama.cpp uninstalled successfully[/green]")
                return True
            else:
                logger.info("llama.cpp is not installed")
                console.print("[yellow]⚠️ llama.cpp is not installed[/yellow]")
                return True

        except Exception as e:
            logger.error(f"Failed to uninstall llama.cpp: {e}")
            console.print(f"[red]❌ Failed to uninstall llama.cpp: {e}[/red]")
            return False


# Global instance
_llama_cpp_manager: Optional[LlamaCppManager] = None


def get_llama_cpp_manager() -> LlamaCppManager:
    """Get the global llama.cpp manager instance."""
    global _llama_cpp_manager
    if _llama_cpp_manager is None:
        _llama_cpp_manager = LlamaCppManager()
    return _llama_cpp_manager
