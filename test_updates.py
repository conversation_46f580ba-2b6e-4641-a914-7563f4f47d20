#!/usr/bin/env python3
"""
Test script for the updated Reverie CLI features.
"""

import asyncio
import sys
from pathlib import Path

async def test_model_manager():
    """Test the updated model manager."""
    print("🔍 Testing Model Manager...")
    
    try:
        from reverie_cli.models.manager import get_model_manager
        
        model_manager = get_model_manager()
        await model_manager.initialize()
        
        print("✅ Model manager initialized")
        
        # Test model scanning
        await model_manager.scan_local_models()
        models = model_manager.list_models()
        
        print(f"✅ Found {len(models)} local models")
        
        for model in models:
            print(f"  - {model.name} ({model.backend.value}, {model.status.value})")
        
        return True
        
    except Exception as e:
        print(f"❌ Model manager test failed: {e}")
        return False

async def test_llama_cpp_python():
    """Test llama-cpp-python availability."""
    print("\n🔍 Testing llama-cpp-python...")
    
    try:
        import llama_cpp
        print("✅ llama-cpp-python is available")
        print(f"  Version: {llama_cpp.__version__ if hasattr(llama_cpp, '__version__') else 'Unknown'}")
        return True
    except ImportError:
        print("❌ llama-cpp-python not installed")
        print("  Install with: pip install llama-cpp-python")
        return False

async def test_api_routes():
    """Test API route availability."""
    print("\n🔍 Testing API Routes...")
    
    try:
        from reverie_cli.api.routes import web
        print("✅ Web routes module available")
        
        # Check if router exists
        if hasattr(web, 'router'):
            print("✅ Web router configured")
        else:
            print("❌ Web router not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API routes test failed: {e}")
        return False

async def test_client_files():
    """Test client files existence."""
    print("\n🔍 Testing Client Files...")
    
    client_dir = Path("reverie-cli-client")
    required_files = [
        "reverie_client.py",
        "build.bat",
        "README.md"
    ]
    
    all_exist = True
    
    for file_name in required_files:
        file_path = client_dir / file_name
        if file_path.exists():
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

async def test_directory_structure():
    """Test directory structure."""
    print("\n🔍 Testing Directory Structure...")
    
    required_dirs = [
        Path("models/llm"),
        Path("models/llm/gguf"),
        Path("models/llm/transformers"),
        Path("loader"),
        Path("loader/llama.cpp"),
        Path("reverie-cli-client")
    ]
    
    all_exist = True
    
    for dir_path in required_dirs:
        if dir_path.exists():
            print(f"✅ {dir_path} exists")
        else:
            print(f"❌ {dir_path} missing")
            all_exist = False
    
    return all_exist

async def test_console_commands():
    """Test console command availability."""
    print("\n🔍 Testing Console Commands...")
    
    try:
        from reverie_cli.core.console import InteractiveConsole
        
        console = InteractiveConsole()
        
        # Check if new commands exist
        required_commands = ["scan", "help", "models", "stop"]
        
        all_exist = True
        for cmd in required_commands:
            if cmd in console.commands:
                print(f"✅ Command '{cmd}' available")
            else:
                print(f"❌ Command '{cmd}' missing")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Console commands test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Testing Reverie CLI Updates")
    print("=" * 50)
    
    tests = [
        ("Directory Structure", test_directory_structure()),
        ("Model Manager", test_model_manager()),
        ("llama-cpp-python", test_llama_cpp_python()),
        ("API Routes", test_api_routes()),
        ("Client Files", test_client_files()),
        ("Console Commands", test_console_commands()),
    ]
    
    results = []
    
    for test_name, test_coro in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = await test_coro
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test '{test_name}' crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Results Summary")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Tests interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
