@echo off
setlocal enabledelayedexpansion

:: =============================================================================
:: Reverie CLI - Model Loader Downloader Script
:: =============================================================================

title Reverie CLI - Model Loader Downloader

echo.
echo ===============================================================================
echo                    Reverie CLI - Model Loader Downloader
echo                   Download and Setup Model Loading Software
echo ===============================================================================
echo.

:: Configuration
set "PROJECT_DIR=%~dp0"
set "LOADER_DIR=%PROJECT_DIR%loader"
set "LLAMA_CPP_DIR=%LOADER_DIR%\llama.cpp"
set "TEMP_DIR=%TEMP%\reverie_loader_download"

echo [INFO] Project Directory: %PROJECT_DIR%
echo [INFO] Loader Directory: %LOADER_DIR%
echo.

:: Create necessary directories
echo [INFO] Creating loader directories...
if not exist "%LOADER_DIR%" mkdir "%LOADER_DIR%"
if not exist "%LLAMA_CPP_DIR%" mkdir "%LLAMA_CPP_DIR%"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"
echo [SUCCESS] Directories created

:: Available loaders configuration
echo.
echo [INFO] Available Model Loaders:
echo.
echo 1. llama.cpp (CUDA 12.4) - For GGUF models with GPU acceleration
echo 2. llama.cpp (CPU) - For GGUF models with CPU-only support
echo 3. llama.cpp (CUDA 11.8) - For GGUF models with older CUDA support
echo 4. Exit without downloading
echo.

set /p "choice=Please select a loader to download (1-4): "

if "%choice%"=="1" goto download_cuda124
if "%choice%"=="2" goto download_cpu
if "%choice%"=="3" goto download_cuda118
if "%choice%"=="4" goto exit_script
echo [ERROR] Invalid choice. Please run the script again.
goto exit_script

:download_cuda124
set "DOWNLOAD_URL=https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cuda-12.4-x64.zip"
set "DOWNLOAD_FILE=llama-b6106-bin-win-cuda-12.4-x64.zip"
set "LOADER_TYPE=llama.cpp CUDA 12.4"
goto :start_download

:download_cpu
set "DOWNLOAD_URL=https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cpu-x64.zip"
set "DOWNLOAD_FILE=llama-b6106-bin-win-cpu-x64.zip"
set "LOADER_TYPE=llama.cpp CPU"
goto :start_download

:download_cuda118
set "DOWNLOAD_URL=https://github.com/ggml-org/llama.cpp/releases/download/b6106/llama-b6106-bin-win-cuda-11.8-x64.zip"
set "DOWNLOAD_FILE=llama-b6106-bin-win-cuda-11.8-x64.zip"
set "LOADER_TYPE=llama.cpp CUDA 11.8"
goto :start_download

:start_download
echo.
echo ===============================================================================
echo                    Downloading %LOADER_TYPE%
echo ===============================================================================
echo.

echo [INFO] Download URL: %DOWNLOAD_URL%
echo [INFO] Target file: %DOWNLOAD_FILE%
echo [INFO] Temporary directory: %TEMP_DIR%
echo.

:: Check if PowerShell is available for download
powershell -Command "Get-Command Invoke-WebRequest" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] PowerShell with Invoke-WebRequest not available
    echo [INFO] Please download manually from: %DOWNLOAD_URL%
    echo [INFO] Extract to: %LLAMA_CPP_DIR%
    pause
    goto exit_script
)

:: Download the file
echo [PROGRESS] Downloading %LOADER_TYPE%...
echo [INFO] This may take several minutes depending on your internet connection...
echo.

set "RETRY_COUNT=0"
:download_retry
powershell -Command "& {$ProgressPreference = 'Continue'; try { Invoke-WebRequest -Uri '%DOWNLOAD_URL%' -OutFile '%TEMP_DIR%\%DOWNLOAD_FILE%' -UseBasicParsing; Write-Host '[SUCCESS] Download completed successfully' } catch { Write-Host '[ERROR] Download failed:' $_.Exception.Message; exit 1 }}"
if errorlevel 1 (
    set /a "RETRY_COUNT+=1"
    if !RETRY_COUNT! lss 3 (
        echo [INFO] Retrying download... (Attempt !RETRY_COUNT!/3)
        timeout /t 5 >nul
        goto download_retry
    ) else (
        echo [ERROR] Download failed after 3 attempts. Please try again later.
        echo [INFO] You can also download manually from: %DOWNLOAD_URL%
        echo [INFO] Extract to: %LLAMA_CPP_DIR%
        goto cleanup_and_exit
    )
)

if errorlevel 1 (
    echo.
    echo [ERROR] Download failed. Please check your internet connection and try again.
    echo [INFO] Manual download URL: %DOWNLOAD_URL%
    pause
    goto cleanup_and_exit
)

echo [SUCCESS] Download completed: %TEMP_DIR%\%DOWNLOAD_FILE%

:: Extract the archive
echo.
echo [PROGRESS] Extracting archive...
echo [INFO] Extracting to: %LLAMA_CPP_DIR%

:: Check if the file exists
if not exist "%TEMP_DIR%\%DOWNLOAD_FILE%" (
    echo [ERROR] Downloaded file not found: %TEMP_DIR%\%DOWNLOAD_FILE%
    goto cleanup_and_exit
)

:: Extract using PowerShell
powershell -Command "& {try { Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%TEMP_DIR%\%DOWNLOAD_FILE%', '%TEMP_DIR%\extracted'); Write-Host '[SUCCESS] Archive extracted successfully' } catch { Write-Host '[ERROR] Extraction failed:' $_.Exception.Message; exit 1 }}"

if errorlevel 1 (
    echo [ERROR] Failed to extract archive
    goto cleanup_and_exit
)

:: Find and copy the executable files
echo [PROGRESS] Locating executable files...

:: Look for llama-cli.exe in the extracted directory
set "LLAMA_CLI_FOUND="
for /r "%TEMP_DIR%\extracted" %%f in (llama-cli.exe) do (
    if exist "%%f" (
        set "LLAMA_CLI_FOUND=%%f"
        echo [SUCCESS] Found llama-cli.exe: %%f
    )
)

:: Look for llama-server.exe in the extracted directory
set "LLAMA_SERVER_FOUND="
for /r "%TEMP_DIR%\extracted" %%f in (llama-server.exe) do (
    if exist "%%f" (
        set "LLAMA_SERVER_FOUND=%%f"
        echo [SUCCESS] Found llama-server.exe: %%f
    )
)

:: Copy executable files to target directory
echo [PROGRESS] Installing executable files...

if defined LLAMA_CLI_FOUND (
    copy "!LLAMA_CLI_FOUND!" "%LLAMA_CPP_DIR%\llama-cli.exe" >nul
    if errorlevel 1 (
        echo [ERROR] Failed to copy llama-cli.exe
        goto cleanup_and_exit
    )
    echo [SUCCESS] Installed: %LLAMA_CPP_DIR%\llama-cli.exe
) else (
    echo [WARNING] llama-cli.exe not found in archive
)

if defined LLAMA_SERVER_FOUND (
    copy "!LLAMA_SERVER_FOUND!" "%LLAMA_CPP_DIR%\llama-server.exe" >nul
    if errorlevel 1 (
        echo [ERROR] Failed to copy llama-server.exe
        goto cleanup_and_exit
    )
    echo [SUCCESS] Installed: %LLAMA_CPP_DIR%\llama-server.exe
) else (
    echo [WARNING] llama-server.exe not found in archive
)

:: Copy any additional files (DLLs, etc.)
echo [PROGRESS] Copying additional files...
for /r "%TEMP_DIR%\extracted" %%f in (*.dll) do (
    copy "%%f" "%LLAMA_CPP_DIR%\" >nul 2>&1
)

:: Create version info file
echo [PROGRESS] Creating version information...
echo %LOADER_TYPE% > "%LLAMA_CPP_DIR%\version.txt"
echo Downloaded on: %DATE% %TIME% >> "%LLAMA_CPP_DIR%\version.txt"
echo Source: %DOWNLOAD_URL% >> "%LLAMA_CPP_DIR%\version.txt"

:: Verify installation
echo.
echo [PROGRESS] Verifying installation...
if exist "%LLAMA_CPP_DIR%\llama-cli.exe" (
    echo [SUCCESS] llama-cli.exe installed successfully
) else (
    echo [ERROR] llama-cli.exe not found after installation
)

if exist "%LLAMA_CPP_DIR%\llama-server.exe" (
    echo [SUCCESS] llama-server.exe installed successfully
) else (
    echo [WARNING] llama-server.exe not found after installation
)

:: Test the executable
echo [PROGRESS] Testing executable...
"%LLAMA_CPP_DIR%\llama-cli.exe" --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] llama-cli.exe test failed - may need additional dependencies
) else (
    echo [SUCCESS] llama-cli.exe is working correctly
)

goto cleanup_and_success

:cleanup_and_success
echo.
echo ===============================================================================
echo                           Installation Complete!
echo ===============================================================================
echo.
echo [SUCCESS] %LOADER_TYPE% installed successfully
echo [SUCCESS] Installation directory: %LLAMA_CPP_DIR%
echo [SUCCESS] Executable: %LLAMA_CPP_DIR%\llama-cli.exe
echo.
echo [INFO] You can now use GGUF models with Reverie CLI
echo [INFO] Place your .gguf model files in: %PROJECT_DIR%models\llm\gguf\
echo.

:cleanup_and_exit
echo [PROGRESS] Cleaning up temporary files...
if exist "%TEMP_DIR%" (
    rmdir /s /q "%TEMP_DIR%" >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Failed to clean up temporary directory: %TEMP_DIR%
        echo [INFO] You may manually delete it to free up disk space
    ) else (
        echo [SUCCESS] Temporary files cleaned up
    )
)

:exit_script
echo.
echo [INFO] Script completed. Press any key to exit.
pause >nul
exit /b 0
